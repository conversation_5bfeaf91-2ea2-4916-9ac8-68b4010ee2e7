/**
 * Credit Payment Processing Tests
 * 
 * Tests for credit payment creation, transaction handling, and lock timeout prevention
 */

const { describe, it, beforeEach, afterEach, expect } = require('@jest/globals');
const PaymentTransactionService = require('../src/services/payment-transaction-service');
const { databaseManager } = require('../src/config/database-manager');
const { v4: uuidv4 } = require('uuid');

describe('Credit Payment Processing', () => {
  let paymentService;
  let testTransactionId;
  let testMethodId;

  beforeEach(async () => {
    paymentService = new PaymentTransactionService();
    
    // Initialize database connection
    await databaseManager.initialize();
    
    // Create a test transaction
    const transactionData = {
      staffId: 'test-staff-id',
      totalAmount: 100.00,
      currency: 'KES',
      paymentMethods: [{
        methodType: 'credit',
        methodName: 'Credit Payment',
        amount: 100.00
      }],
      notes: 'Test credit payment transaction'
    };
    
    const result = await paymentService.initiateTransaction(transactionData);
    testTransactionId = result.transactionId;
    testMethodId = result.paymentMethods[0].id;
  });

  afterEach(async () => {
    // Clean up test data
    if (testTransactionId) {
      try {
        await databaseManager.executeQuery(
          'DELETE FROM credit_payments WHERE payment_method_id IN (SELECT id FROM payment_methods_used WHERE transaction_id = ?)',
          [testTransactionId]
        );
        await databaseManager.executeQuery(
          'DELETE FROM payment_methods_used WHERE transaction_id = ?',
          [testTransactionId]
        );
        await databaseManager.executeQuery(
          'DELETE FROM payment_transactions WHERE id = ?',
          [testTransactionId]
        );
      } catch (error) {
        console.warn('Cleanup error:', error.message);
      }
    }
  });

  describe('Credit Payment Creation', () => {
    it('should create credit payment without lock timeout', async () => {
      const processingData = {
        customerId: 'test-customer-id',
        customerName: 'Test Customer',
        customerPhone: '254712345678',
        creditLimit: 50000,
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        paymentTerms: 'Net 30',
        notes: 'Test credit payment',
        existingBalance: 0
      };

      const result = await paymentService.processPaymentMethod(
        testMethodId,
        'test-staff-id',
        processingData
      );

      expect(result.success).toBe(true);
      expect(result.status).toBe('completed');
      expect(result.error).toBeUndefined();
    });

    it('should handle concurrent credit payment processing', async () => {
      const processingData = {
        customerId: 'test-customer-id',
        customerName: 'Test Customer',
        customerPhone: '254712345678',
        creditLimit: 50000,
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        paymentTerms: 'Net 30',
        notes: 'Concurrent test credit payment',
        existingBalance: 0
      };

      // Create multiple concurrent credit payment requests
      const promises = Array(3).fill().map(async (_, index) => {
        // Create separate transactions for concurrent testing
        const concurrentTransactionData = {
          staffId: `test-staff-id-${index}`,
          totalAmount: 100.00,
          currency: 'KES',
          paymentMethods: [{
            methodType: 'credit',
            methodName: 'Credit Payment',
            amount: 100.00
          }],
          notes: `Concurrent test credit payment ${index}`
        };
        
        const transactionResult = await paymentService.initiateTransaction(concurrentTransactionData);
        const methodId = transactionResult.paymentMethods[0].id;
        
        const result = await paymentService.processPaymentMethod(
          methodId,
          `test-staff-id-${index}`,
          {
            ...processingData,
            customerName: `Test Customer ${index}`,
            notes: `Concurrent test credit payment ${index}`
          }
        );
        
        // Clean up
        await databaseManager.executeQuery(
          'DELETE FROM credit_payments WHERE payment_method_id IN (SELECT id FROM payment_methods_used WHERE transaction_id = ?)',
          [transactionResult.transactionId]
        );
        await databaseManager.executeQuery(
          'DELETE FROM payment_methods_used WHERE transaction_id = ?',
          [transactionResult.transactionId]
        );
        await databaseManager.executeQuery(
          'DELETE FROM payment_transactions WHERE id = ?',
          [transactionResult.transactionId]
        );
        
        return result;
      });

      const results = await Promise.all(promises);
      
      // All concurrent requests should succeed
      results.forEach((result, index) => {
        expect(result.success).toBe(true);
        expect(result.status).toBe('completed');
        expect(result.error).toBeUndefined();
      });
    });

    it('should validate required credit payment fields', async () => {
      const processingData = {
        // Missing customerName
        customerId: 'test-customer-id',
        customerPhone: '254712345678',
        creditLimit: 50000
      };

      const result = await paymentService.processPaymentMethod(
        testMethodId,
        'test-staff-id',
        processingData
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Customer name is required');
    });

    it('should handle credit limit validation', async () => {
      const processingData = {
        customerId: 'test-customer-id',
        customerName: 'Test Customer',
        customerPhone: '254712345678',
        creditLimit: 50, // Low credit limit
        existingBalance: 0,
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        paymentTerms: 'Net 30',
        notes: 'Test credit limit validation'
      };

      const result = await paymentService.processPaymentMethod(
        testMethodId,
        'test-staff-id',
        processingData
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Credit limit exceeded');
    });
  });

  describe('Transaction Status Updates', () => {
    it('should update transaction status after credit payment processing', async () => {
      const processingData = {
        customerId: 'test-customer-id',
        customerName: 'Test Customer',
        customerPhone: '254712345678',
        creditLimit: 50000,
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        paymentTerms: 'Net 30',
        notes: 'Test transaction status update',
        existingBalance: 0
      };

      await paymentService.processPaymentMethod(
        testMethodId,
        'test-staff-id',
        processingData
      );

      // Check transaction status
      const transactionDetails = await paymentService.getTransactionDetails(testTransactionId);
      expect(transactionDetails.success).toBe(true);
      expect(transactionDetails.transaction.payment_status).toBe('completed');
      expect(transactionDetails.creditPayments).toHaveLength(1);
    });
  });
});
