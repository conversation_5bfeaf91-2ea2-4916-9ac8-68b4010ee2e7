/**
 * Utility functions for loyalty points processing and fallback handling
 */

import { Customer, CustomerLoyaltyData } from '@/src/types/shopify';

/**
 * Extract loyalty points from customer note field
 * Parses note format: "Loyalty Points: 45 | Credit Limit: KSh 30,000.00"
 */
export function extractLoyaltyPointsFromNote(note?: string): number {
  if (!note) return 0;
  
  // Match pattern: "Loyalty Points: [number]"
  const loyaltyPointsMatch = note.match(/Loyalty Points:\s*(\d+)/i);
  
  if (loyaltyPointsMatch && loyaltyPointsMatch[1]) {
    const points = parseInt(loyaltyPointsMatch[1], 10);
    return isNaN(points) ? 0 : points;
  }
  
  return 0;
}

/**
 * Extract credit limit from customer note field
 * Parses note format: "Loyalty Points: 45 | Credit Limit: KSh 30,000.00"
 */
export function extractCreditLimitFromNote(note?: string): number {
  if (!note) return 0;
  
  // Match pattern: "Credit Limit: KSh [number with commas]"
  const creditLimitMatch = note.match(/Credit Limit:\s*KSh\s*([\d,]+(?:\.\d{2})?)/i);
  
  if (creditLimitMatch && creditLimitMatch[1]) {
    // Remove commas and parse as float
    const creditLimit = parseFloat(creditLimitMatch[1].replace(/,/g, ''));
    return isNaN(creditLimit) ? 0 : creditLimit;
  }
  
  return 0;
}

/**
 * Get effective loyalty points with fallback to note field
 * Priority: loyaltyData.loyaltyPoints > note field > 0
 */
export function getEffectiveLoyaltyPoints(customer: Customer): number {
  // First try to get points from loyaltyData
  if (customer.loyaltyData && customer.loyaltyData.loyaltyPoints > 0) {
    return customer.loyaltyData.loyaltyPoints;
  }
  
  // Fallback to note field if loyaltyData points is zero or missing
  const notePoints = extractLoyaltyPointsFromNote(customer.note);
  if (notePoints > 0) {
    console.log(`📝 Using loyalty points from note for customer ${customer.displayName}: ${notePoints} points`);
    return notePoints;
  }
  
  // Final fallback to zero
  return 0;
}

/**
 * Get enhanced loyalty data with note fallbacks
 * Combines loyaltyData with note field information when available
 */
export function getEnhancedLoyaltyData(customer: Customer): CustomerLoyaltyData | null {
  const effectivePoints = getEffectiveLoyaltyPoints(customer);
  
  // If no points available from either source, return null
  if (effectivePoints === 0) {
    return null;
  }
  
  // If we have loyaltyData but points are zero, enhance with note data
  if (customer.loyaltyData) {
    return {
      ...customer.loyaltyData,
      loyaltyPoints: effectivePoints, // Use effective points (note fallback if needed)
    };
  }
  
  // Create basic loyalty data from note information
  const notePoints = extractLoyaltyPointsFromNote(customer.note);
  if (notePoints > 0) {
    return {
      customerId: customer.id,
      loyaltyPoints: notePoints,
      tier: 'bronze', // Default tier when only note data available
      tierBenefits: {
        multiplier: 1,
        pointsPerKsh: 1,
      },
      totalPurchases: parseFloat(customer.totalSpent) || 0,
      totalOrders: typeof customer.ordersCount === 'string' 
        ? parseInt(customer.ordersCount, 10) || 0 
        : customer.ordersCount || 0,
      lastPurchase: customer.updatedAt,
      memberSince: customer.createdAt,
      redemptionInfo: {
        pointsPerKsh: 10,
        availableDiscount: notePoints / 10, // Basic calculation
        minRedemption: 50,
      },
    };
  }
  
  return null;
}

/**
 * Format loyalty points for display
 */
export function formatLoyaltyPoints(points: number): string {
  return points.toLocaleString();
}

/**
 * Get loyalty tier color for UI display
 */
export function getLoyaltyTierColor(tier: string): string {
  switch (tier.toLowerCase()) {
    case 'bronze':
      return '#CD7F32';
    case 'silver':
      return '#C0C0C0';
    case 'gold':
      return '#FFD700';
    case 'platinum':
      return '#E5E4E2';
    default:
      return '#CD7F32'; // Default to bronze
  }
}

/**
 * Check if customer has any loyalty data (either from API or note)
 */
export function hasLoyaltyData(customer: Customer): boolean {
  return getEffectiveLoyaltyPoints(customer) > 0;
}

/**
 * Get loyalty data source for debugging/logging
 */
export function getLoyaltyDataSource(customer: Customer): 'api' | 'note' | 'none' {
  if (customer.loyaltyData && customer.loyaltyData.loyaltyPoints > 0) {
    return 'api';
  }
  
  if (extractLoyaltyPointsFromNote(customer.note) > 0) {
    return 'note';
  }
  
  return 'none';
}
