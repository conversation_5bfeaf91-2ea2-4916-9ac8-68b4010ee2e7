import { SafeAreaWrapper } from "@/components/layout/SafeAreaWrapper";
import { ConfirmationModal } from "@/components/ui/ConfirmationModal";
import { ErrorModal } from "@/components/ui/ErrorModal";
import { ModernButton } from "@/components/ui/ModernButton";
import { ModernCard } from "@/components/ui/ModernCard";
import { SuccessModal } from "@/components/ui/SuccessModal";
import { Colors } from "@/constants/Colors";
// Removed old constants - using hardcoded values now
import { useThemeColor } from "@/hooks/useThemeColor";
import { useSession } from "@/src/contexts/AuthContext";
import { useLocation } from "@/src/contexts/LocationContext";
import { getAPIClient } from "@/src/services/api/dukalink-client";
import * as Device from "expo-device";
import { useRouter } from "expo-router";
import React, { useState } from "react";
import {
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  View,
} from "react-native";

const POSLoginScreen: React.FC = () => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Modal states
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showDemoModal, setShowDemoModal] = useState(false);
  const [modalData, setModalData] = useState({
    title: "",
    message: "",
    successData: null as any,
  });

  const router = useRouter();
  const { setCurrentLocation } = useLocation();
  const { setPosAuth } = useSession();
  const backgroundColor = useThemeColor({}, "background");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const surfaceColor = useThemeColor({}, "surface");
  const borderColor = useThemeColor({}, "border");

  const handleLogin = async () => {
    if (!username.trim() || !password.trim()) {
      setModalData({
        title: "Error",
        message: "Please enter both username and password",
        successData: null,
      });
      setShowErrorModal(true);
      return;
    }

    setIsLoading(true);
    try {
      // Gather device information for terminal assignment
      const deviceInfo = {
        deviceId: Device.osInternalBuildId || `device-${Date.now()}`,
        platform: Platform.OS,
        deviceName: Device.deviceName || "Unknown Device",
        modelName: Device.modelName || "Unknown Model",
        osVersion: Device.osVersion || "Unknown",
        isTablet: Device.deviceType === 2, // DeviceType.TABLET = 2
        brand: Device.brand || "Unknown",
      };

      const apiClient = getAPIClient();
      const response = await apiClient.posLoginWithDevice(
        username,
        password,
        deviceInfo
      );

      if (response.success && response.data) {
        // Store the token and user info
        await apiClient.setPOSToken(response.data.token);

        // Update session context with POS authentication
        setPosAuth(response.data.token, response.data.user);

        // Auto-set location from terminal assignment
        if (response.data.location) {
          setCurrentLocation({
            id: response.data.location.id,
            name: response.data.location.name,
            address: {}, // Will be populated from API if needed
            active: true,
          });
        }

        const terminalInfo = response.data.terminal
          ? `\nTerminal: ${response.data.terminal.name}`
          : "";
        const locationInfo = response.data.location
          ? `\nLocation: ${response.data.location.name}`
          : "";

        setModalData({
          title: "Login Successful",
          message: `Welcome ${response.data.user.name}!${terminalInfo}${locationInfo}`,
          successData: response.data,
        });
        setShowSuccessModal(true);
      } else {
        setModalData({
          title: "Login Failed",
          message: response.error || "Invalid credentials",
          successData: null,
        });
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error("Login error:", error);
      setModalData({
        title: "Error",
        message: "Login failed. Please try again.",
        successData: null,
      });
      setShowErrorModal(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDemoLogin = () => {
    setModalData({
      title: "Demo Credentials",
      message: "Choose demo credentials to use:",
      successData: null,
    });
    setShowDemoModal(true);
  };

  // Modal handlers
  const handleSuccessModalContinue = () => {
    setShowSuccessModal(false);
    router.replace("/(tabs)");
  };

  const handleDemoCredentialSelect = (username: string, password: string) => {
    setUsername(username);
    setPassword(password);
    setShowDemoModal(false);
  };

  return (
    <SafeAreaWrapper style={[styles.container, { backgroundColor }]}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={[styles.title, { color: Colors.light.primary }]}>
              Dukalink POS
            </Text>
            <Text style={[styles.subtitle, { color: textSecondary }]}>
              Point of Sale System
            </Text>
          </View>

          <ModernCard style={styles.form} variant="elevated">
            <View style={styles.inputContainer}>
              <Text style={[styles.label, { color: textColor }]}>Username</Text>
              <TextInput
                style={[
                  styles.input,
                  {
                    backgroundColor: surfaceColor,
                    borderColor: borderColor,
                    color: textColor,
                  },
                ]}
                value={username}
                onChangeText={setUsername}
                placeholder="Enter your username"
                placeholderTextColor={textSecondary}
                autoCapitalize="none"
                autoCorrect={false}
                editable={!isLoading}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.label, { color: textColor }]}>Password</Text>
              <TextInput
                style={[
                  styles.input,
                  {
                    backgroundColor: surfaceColor,
                    borderColor: borderColor,
                    color: textColor,
                  },
                ]}
                value={password}
                onChangeText={setPassword}
                placeholder="Enter your password"
                placeholderTextColor={textSecondary}
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
                editable={!isLoading}
              />
            </View>

            <ModernButton
              title={isLoading ? "Logging in..." : "Login"}
              onPress={handleLogin}
              loading={isLoading}
              disabled={isLoading}
              style={styles.loginButton}
            />

             <ModernButton
              title="Demo Credentials"
              onPress={handleDemoLogin}
              variant="ghost"
              disabled={isLoading}
              style={styles.demoButton}
            /> 
          </ModernCard>

          <View style={styles.footer}>
            <Text style={[styles.footerText, { color: textSecondary }]}>
              For store setup and management, contact your administrator
            </Text>
          </View>
        </View>
      </KeyboardAvoidingView>

      {/* Error Modal */}
      <ErrorModal
        visible={showErrorModal}
        onClose={() => setShowErrorModal(false)}
        title={modalData.title}
        message={modalData.message}
      />

      {/* Success Modal */}
      <SuccessModal
        visible={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title={modalData.title}
        message={modalData.message}
        actions={[
          {
            title: "Continue",
            onPress: handleSuccessModalContinue,
            variant: "primary",
            icon: "arrow.right",
          },
        ]}
      />

      {/* Demo Credentials Modal */}
      <ConfirmationModal
        visible={showDemoModal}
        onClose={() => setShowDemoModal(false)}
        title={modalData.title}
        message="Click the buttons below to use demo credentials for different user roles."
        icon="person.3"
        actions={[
          {
            title: "Use Cashier",
            onPress: () =>
              handleDemoCredentialSelect("cashier1", "password123"),
            variant: "outline",
          },
          {
            title: "Use Manager",
            onPress: () => handleDemoCredentialSelect("manager1", "manager123"),
            variant: "outline",
          },
          {
            title: "Use Admin",
            onPress: () => handleDemoCredentialSelect("admin1", "admin123"),
            variant: "primary",
          },
          {
            title: "Cancel",
            onPress: () => setShowDemoModal(false),
            variant: "ghost",
          },
        ]}
      />
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: "center",
    padding: 24,
  },
  header: {
    alignItems: "center",
    marginBottom: 48,
  },
  title: {
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
  },
  form: {
    marginBottom: 32,
  },
  inputContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
  },
  loginButton: {
    marginBottom: 16,
  },
  demoButton: {
    marginTop: 8,
  },
  footer: {
    alignItems: "center",
  },
  footerText: {
    fontSize: 12,
    opacity: 0.7,
    textAlign: "center",
    lineHeight: 20,
  },
});

export default POSLoginScreen;
