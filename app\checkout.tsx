import { ScreenWrapper } from "@/components/layout/ScreenWrapper";
import { ErrorModal } from "@/components/ui/ErrorModal";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernButton } from "@/components/ui/ModernButton";
import { ModernCard } from "@/components/ui/ModernCard";
import { SuccessModal } from "@/components/ui/SuccessModal";

import {
  ShippingOptionsModal,
  ShippingData,
} from "@/components/checkout/ShippingOptionsModal";
import { ShippingDecisionSection } from "@/src/components/checkout/ShippingDecisionSection";
import { useThemeColor } from "@/hooks/useThemeColor";
import { useSession } from "@/src/contexts/AuthContext";
import { useCustomer } from "@/src/contexts/CustomerContext";
import { useLocation } from "@/src/contexts/LocationContext";
import { useSalesAgent } from "@/src/contexts/SalesAgentContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useUnifiedCart } from "@/src/hooks/useUnifiedCart";
import { useResponsiveLayout } from "@/src/hooks/useResponsiveLayout";

import { getAPIClient } from "@/src/services/api/dukalink-client";
// REMOVED: Legacy receipt printing services - now using UnifiedReceiptManager only
import { PaymentService } from "@/src/services/payment-service";

import { UnifiedReceiptManager } from "@/src/services/UnifiedReceiptManager";
import { enhancedPaymentService } from "@/src/services/enhanced-payment-service";
import { useAppDispatch, useAppSelector } from "@/src/store";
import { createCustomer } from "@/src/store/slices/customerSlice";
import {
  loyaltyOrderCompletionService,
  useLoyaltyCacheInvalidation,
} from "@/src/services/loyalty-order-completion";
import PaymentFlowManager, {
  PaymentFlowResult,
} from "@/src/components/payment/PaymentFlowManager";
import {
  completeActiveTicket,
  selectActiveTicket,
  resumeCustomerTicket,
  mergeCustomerTicket,
  setActiveTicketCustomer,
} from "@/src/store/slices/ticketSlice";
import { Customer, Order } from "@/src/types/shopify";
import { formatCurrency } from "@/src/utils/currencyUtils";
import { CrossPlatformStorage } from "@/src/utils/storage";
import { clearCartFromStorage } from "@/src/utils/cartPersistence";
import { useRouter } from "expo-router";
import React, { useCallback, useEffect, useState, useMemo } from "react";
import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

import CartInventoryValidator from "@/components/ui/CartInventoryValidator";
import { ConfirmationModal } from "@/components/ui/ConfirmationModal";
import { OrderCompletionModal } from "@/components/ui/OrderCompletionModal";
import {
  PreOrderPrinterCheckModal,
  PreOrderPrinterCheckResult,
} from "@/components/ui/PreOrderPrinterCheckModal";
import { FloatingActionButton } from "@/src/components/ui/FloatingActionButton";
import { useNavigation } from "@/src/contexts/NavigationContext";
import { useInventoryManagement } from "@/src/hooks/useInventoryManagement";
import { CustomerTicketResumeModal } from "@/components/ui/CustomerTicketResumeModal";
import {
  CustomerTicket,
  TicketResumeAction,
} from "@/src/types/customerTickets";
import { CustomerCardWithLoyalty } from "@/src/components/customer/CustomerCardWithLoyalty";

// Helper function to convert SelectedCustomer to Customer
const selectedCustomerToCustomer = (selectedCustomer: any): Customer => ({
  id: selectedCustomer.id,
  firstName: selectedCustomer.firstName,
  lastName: selectedCustomer.lastName || "",
  displayName: selectedCustomer.displayName,
  email: selectedCustomer.email,
  phone: selectedCustomer.phone,
  addresses: [],
  ordersCount: selectedCustomer.ordersCount || 0,
  totalSpent: selectedCustomer.totalSpent || "0",
  tags: [],
  createdAt: "",
  updatedAt: "",
});

const CheckoutScreen: React.FC = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { user, isPosAuthenticated: isAuthenticated } = useSession();
  const { selectedCustomer, setSelectedCustomer, clearSelectedCustomer } =
    useCustomer();
  const { selectedAgent, setSelectedAgent, clearSelectedAgent } =
    useSalesAgent();
  const { currentLocation } = useLocation();
  const { setCurrentTitle } = useNavigation();
  const cart = useUnifiedCart();
  const activeTicket = useAppSelector(selectActiveTicket);
  const activeTicketId = useAppSelector(
    (state) => state.tickets.activeTicketId
  );

  // State to preserve cart data during transitions
  const [stableCartData, setStableCartData] = useState({
    items: cart.items || [],
    total: cart.total || 0,
    subtotal: cart.subtotal || 0,
    itemCount: cart.itemCount || 0,
  });

  // Use unified cart data with fallback to ensure consistency
  const cartItems = useMemo(() => cart.items || [], [cart.items]);
  const cartTotal = useMemo(() => cart.total || 0, [cart.total]);
  const cartSubtotal = useMemo(() => cart.subtotal || 0, [cart.subtotal]);
  const itemCount = useMemo(() => cart.itemCount || 0, [cart.itemCount]);

  // Update stable cart data whenever cart data changes (including discounts)
  useEffect(() => {
    setStableCartData({
      items: cartItems,
      total: cartTotal,
      subtotal: cartSubtotal,
      itemCount: itemCount,
    });
  }, [cartItems, cartTotal, cartSubtotal, itemCount]);

  // Use stable cart data for rendering to prevent flickering during state transitions
  const displayCartItems = stableCartData.items;
  const displayCartTotal = stableCartData.total;
  const displayCartSubtotal = stableCartData.subtotal;
  const displayItemCount = stableCartData.itemCount;

  // Debug logging for ticket ID changes
  useEffect(() => {
    console.log(
      `🎫 Active ticket ID: ${activeTicketId}, Items count: ${cartItems.length}, Total: ${cartTotal}`
    );
    console.log(
      `🎫 Display Items count: ${displayCartItems.length}, Display Total: ${displayCartTotal}`
    );
    if (activeTicket) {
      console.log(`🎫 Active ticket items: ${activeTicket.items?.length || 0}`);
    }
  }, [
    activeTicketId,
    cartItems.length,
    cartTotal,
    displayCartItems.length,
    displayCartTotal,
    activeTicket,
  ]);

  // Inventory management hook
  const {
    handlePostOrderInventoryUpdate,
    validateCartInventory,
    hasOutOfStockItems,
    adjustCartForInventory,
  } = useInventoryManagement();

  const [isProcessing, setIsProcessing] = useState(false);

  // Payment state (legacy - used for fallback scenarios)
  const [paymentMethod, setPaymentMethod] =
    useState<string>("Enhanced Payment");
  const [transactionCode, setTransactionCode] = useState<string>("");

  // Selection modal states
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [showSalesAgentModal, setShowSalesAgentModal] = useState(false);

  // Customer selection state
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [customerSearchQuery, setCustomerSearchQuery] = useState("");
  const [isLoadingCustomers, setIsLoadingCustomers] = useState(false);

  // Sales agent selection state
  const [salesAgents, setSalesAgents] = useState<any[]>([]);
  const [agentSearchQuery, setAgentSearchQuery] = useState("");
  const [isLoadingSalesAgents, setIsLoadingSalesAgents] = useState(false);

  // Modal states
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [showOrderCompletionModal, setShowOrderCompletionModal] =
    useState(false);
  const [showPreOrderPrinterCheck, setShowPreOrderPrinterCheck] =
    useState(false);
  const [showShippingModal, setShowShippingModal] = useState(false);

  const [showCustomerCreationModal, setShowCustomerCreationModal] =
    useState(false);
  const [showCustomerTicketResumeModal, setShowCustomerTicketResumeModal] =
    useState(false);
  const [customerTickets, setCustomerTickets] = useState<CustomerTicket[]>([]);
  const [isLoadingCustomerTickets, setIsLoadingCustomerTickets] =
    useState(false);
  const [modalData, setModalData] = useState<{
    title: string;
    message: string;
    orderNumber?: string;
    orderTotal?: number;
  }>({ title: "", message: "" });

  // Customer creation form state
  const [customerFormData, setCustomerFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
  });
  const [isCreatingCustomer, setIsCreatingCustomer] = useState(false);

  // Pre-order printer check state
  const [printerCheckResult, setPrinterCheckResult] =
    useState<PreOrderPrinterCheckResult | null>(null);
  const [pendingOrderAction, setPendingOrderAction] = useState<
    "cash" | "mpesa_till" | null
  >(null);

  // Store order data for receipt printing and completion flow
  const [lastOrderData, setLastOrderData] = useState<any>(null);
  const [completionOrderData, setCompletionOrderData] = useState<{
    orderData: any;
    orderNumber: string;
    orderTotal: number;
    paymentMethod: string;
    transactionId?: string;
    printingAlreadyAttempted?: boolean;
    printingWasSuccessful?: boolean;
  } | null>(null);

  // Payment method selection state
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<any>(null);
  const [isPaymentMethodValid, setIsPaymentMethodValid] = useState(false);
  const [isProcessingOrder, setIsProcessingOrder] = useState(false);

  // Discount modal state
  const [showDiscountModal, setShowDiscountModal] = useState(false);
  const [selectedItemForDiscount, setSelectedItemForDiscount] =
    useState<any>(null);
  const [discountType, setDiscountType] = useState<
    "percentage" | "fixed_amount"
  >("percentage");
  const [discountAmount, setDiscountAmount] = useState("");

  // Shipping state - New inline shipping decision
  const [shippingDecision, setShippingDecision] = useState<
    "include" | "none" | null
  >(null);
  const [shippingData, setShippingData] = useState<{
    fee: number;
    data: {
      address: string;
      deliveryMethod: string;
      customerName: string;
      phoneNumber: string;
    } | null;
  }>({ fee: 0, data: null });

  // Calculate total including shipping fee - Updated for new shipping flow
  const finalOrderTotal = useMemo(() => {
    const baseTotal = displayCartTotal;
    const shippingFee = shippingData.fee || 0;
    return baseTotal + shippingFee;
  }, [displayCartTotal, shippingData.fee]);

  // Flow control flags
  const isCustomerSelected = !!selectedCustomer;
  const isShippingDecided = shippingDecision !== null;
  const isPaymentEnabled = isCustomerSelected && isShippingDecided;

  // Loyalty cache invalidation
  const { invalidateLoyaltyCaches } = useLoyaltyCacheInvalidation();

  // Discount functions
  const handleApplyDiscount = () => {
    if (!selectedItemForDiscount) return;

    const amount = parseFloat(discountAmount);
    if (isNaN(amount) || amount <= 0) {
      alert("Please enter a valid discount amount");
      return;
    }

    if (discountType === "percentage" && amount > 100) {
      alert("Percentage discount cannot exceed 100%");
      return;
    }

    const lineTotal =
      parseFloat(selectedItemForDiscount.price) *
      selectedItemForDiscount.quantity;
    if (discountType === "fixed_amount" && amount > lineTotal) {
      alert(
        `Fixed amount discount cannot exceed line total of KSh ${lineTotal.toFixed(
          2
        )}`
      );
      return;
    }

    const discount = {
      type: discountType,
      amount: amount,
    };

    console.log("🏷️ Applying discount:", {
      variantId: selectedItemForDiscount.variantId,
      discount,
      itemTitle: selectedItemForDiscount.title,
    });

    cart.applyItemDiscount(selectedItemForDiscount.variantId, discount);

    // Force a small delay to ensure state updates
    setTimeout(() => {
      console.log("🏷️ Cart state after discount:", {
        items: cart.items.map((item) => ({
          title: item.title,
          discount: item.discount,
          price: item.price,
          quantity: item.quantity,
        })),
        total: cart.total,
        subtotal: cart.subtotal,
      });
    }, 100);

    setShowDiscountModal(false);
    setSelectedItemForDiscount(null);
    setDiscountAmount("");
  };

  const handleRemoveDiscount = () => {
    if (!selectedItemForDiscount) return;

    console.log("🏷️ Removing discount:", {
      variantId: selectedItemForDiscount.variantId,
      itemTitle: selectedItemForDiscount.title,
    });

    cart.applyItemDiscount(selectedItemForDiscount.variantId, null);

    // Force a small delay to ensure state updates
    setTimeout(() => {
      console.log("🏷️ Cart state after removing discount:", {
        items: cart.items.map((item) => ({
          title: item.title,
          discount: item.discount,
          price: item.price,
          quantity: item.quantity,
        })),
        total: cart.total,
        subtotal: cart.subtotal,
      });
    }, 100);

    setShowDiscountModal(false);
    setSelectedItemForDiscount(null);
    setDiscountAmount("");
  };

  // Handle shipping decision update
  const handleShippingUpdate = (shipping: {
    fee: number;
    data: {
      address: string;
      deliveryMethod: string;
      customerName: string;
      phoneNumber: string;
    } | null;
  }) => {
    setShippingData(shipping);
    setShippingDecision(shipping.fee > 0 ? "include" : "none");

    console.log("🚚 Shipping decision updated:", {
      fee: shipping.fee,
      decision: shipping.fee > 0 ? "include" : "none",
      data: shipping.data,
    });
  };

  // Handle payment method selection change
  const handlePaymentMethodChange = (selection: any, isValid: boolean) => {
    setSelectedPaymentMethod(selection);
    setIsPaymentMethodValid(isValid);
  };

  // Comprehensive payment method validation
  const validatePaymentMethodData = (
    paymentMethod: any
  ): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!paymentMethod) {
      errors.push("No payment method selected");
      return { isValid: false, errors };
    }

    const metadata = paymentMethod.metadata || {};
    const methodType = paymentMethod.method?.type;

    switch (methodType) {
      case "cash":
        // Cash is always valid
        break;

      case "mpesa":
        // M-Pesa payment is valid with or without transaction code
        // STK Push or manual transaction code entry are both optional

        // Check if customer phone number is available
        const hasCustomerPhone = !!(
          metadata.customerPhone ||
          metadata.phoneNumber ||
          selectedCustomer?.phone
        );

        if (!hasCustomerPhone) {
          errors.push("M-Pesa payment requires customer phone number");
        }
        break;

      case "credit":
        // Check if customer information is available
        const hasCustomerName = !!(
          metadata.customerName ||
          selectedCustomer?.displayName ||
          selectedCustomer?.firstName
        );

        if (!hasCustomerName) {
          errors.push("Credit payment requires customer name");
        }

        const hasCreditCustomerPhone = !!(
          metadata.customerPhone || selectedCustomer?.phone
        );

        if (!hasCreditCustomerPhone) {
          errors.push("Credit payment requires customer phone number");
        }
        break;

      case "absa_till":
        // ABSA Till transaction code is now optional
        // Payment can proceed without transaction code for manual entry later
        break;

      case "split":
        // For split payments, validate each component method
        const splitMethods =
          metadata.splitMethods || metadata.splitConfig || [];
        if (splitMethods.length === 0) {
          errors.push("Split payment requires at least one payment method");
        } else {
          splitMethods.forEach((splitMethod: any, index: number) => {
            const splitValidation = validatePaymentMethodData({
              method: splitMethod.method,
              metadata: splitMethod.config || splitMethod,
            });
            if (!splitValidation.isValid) {
              errors.push(
                `Split payment method ${
                  index + 1
                }: ${splitValidation.errors.join(", ")}`
              );
            }
          });
        }
        break;

      default:
        errors.push(`Unsupported payment method: ${methodType}`);
    }

    return { isValid: errors.length === 0, errors };
  };

  // Handle enhanced payment flow completion
  const handleEnhancedPaymentComplete = async (result: PaymentFlowResult) => {
    console.log("🔄 Enhanced payment flow completed:", result);

    if (result.success) {
      try {
        // Create order data for Shopify
        const orderData = {
          line_items: cartItems.map((item) => ({
            variant_id: item.variantId,
            quantity: item.quantity,
            price: item.price.toString(),
            title: item.title,
            sku: item.sku,
          })),
          customer: selectedCustomer
            ? {
                id: selectedCustomer.id,
                first_name: selectedCustomer.firstName,
                last_name: selectedCustomer.lastName,
                email: selectedCustomer.email,
                phone: selectedCustomer.phone,
              }
            : undefined,
          financial_status: "pending",
          fulfillment_status: null,
          note: `POS Order - Staff: ${user?.name}, Agent: ${selectedAgent?.name}`,
          note_attributes: [
            { name: "pos_staff_id", value: user?.id || "" },
            { name: "sales_agent_id", value: selectedAgent?.id || "" },
            { name: "location_id", value: currentLocation?.id || "" },
            { name: "payment_transaction_id", value: result.transactionId },
            {
              name: "split_payment",
              value: result.isSplitPayment ? "true" : "false",
            },
          ],
          tags: "pos,enhanced-payment",
        };

        // Complete transaction with Shopify integration
        const shopifyResult =
          await enhancedPaymentService.completeTransactionWithShopify(
            result.transactionId,
            orderData
          );

        if (shopifyResult.success) {
          // Loyalty points are now processed automatically by the backend
          // Extract loyalty result from backend response
          let loyaltyResult = shopifyResult.loyaltyResult || null;

          // If backend didn't process loyalty (fallback), process manually
          if (selectedCustomer && !loyaltyResult?.success) {
            console.log(
              "⚠️ Backend didn't process loyalty points, falling back to manual processing..."
            );
            loyaltyResult = await processLoyaltyPointsWithRetry(
              selectedCustomer,
              result,
              shopifyResult,
              cartItems,
              selectedAgent,
              user,
              selectedPaymentMethod
            );
          } else if (loyaltyResult?.success) {
            console.log(
              "✅ Loyalty points processed automatically by backend:",
              loyaltyResult
            );
          }

          // Show success and handle completion
          const loyaltyMessage = getLoyaltyMessage(
            loyaltyResult,
            selectedCustomer
          );

          setModalData({
            title: "Order Completed Successfully!",
            message: `Order #${
              shopifyResult.orderNumber
            } has been created with ${
              result.paymentMethods.length
            } payment method${
              result.paymentMethods.length > 1 ? "s" : ""
            }.\n\nTotal: KSh ${(result.totalAmount || 0).toFixed(
              2
            )}\nTransaction ID: ${result.transactionId}${loyaltyMessage}`,
            orderNumber: shopifyResult.orderNumber,
            orderTotal: result.totalAmount,
          });
          setShowSuccessModal(true);
        } else {
          throw new Error(
            shopifyResult.error || "Failed to create Shopify order"
          );
        }
      } catch (error) {
        console.error("Error completing enhanced payment order:", error);
        setModalData({
          title: "Order Creation Failed",
          message:
            "Payment was successful but order creation failed. Please contact support.",
        });
        setShowErrorModal(true);
      }
    } else {
      setModalData({
        title: "Payment Failed",
        message: result.error || "Payment processing failed. Please try again.",
      });
      setShowErrorModal(true);
    }
  };

  // Handle completion of the entire order flow (from OrderCompletionModal)
  const handleOrderCompletion = async () => {
    console.log("🔄 Starting order completion with inventory update...");

    // Update inventory before clearing cart
    if (cartItems.length > 0) {
      const inventoryResult = await handlePostOrderInventoryUpdate(cartItems);
      if (inventoryResult.success) {
        console.log("✅ Inventory updated successfully after order completion");
      } else {
        console.error("❌ Inventory update failed:", inventoryResult.error);
        // Continue with order completion even if inventory update fails
      }
    }

    // Final loyalty cache invalidation if customer was involved
    if (selectedCustomer) {
      try {
        // Import and use the proper cache invalidation function
        const { useLoyaltyCacheInvalidation } = await import(
          "@/src/hooks/queries/useLoyalty"
        );

        // Use React Query's invalidation for real-time updates
        invalidateLoyaltyCaches(selectedCustomer.id);
        console.log("✅ Final loyalty cache invalidation completed");

        // Also invalidate customer data cache to refresh loyalty information
        const { getInvalidationKeys } = await import("@/src/lib/queryKeys");
        const invalidationKeys = getInvalidationKeys.onCustomerUpdate(
          selectedCustomer.id
        );

        // Note: In a real implementation, we would use queryClient.invalidateQueries here
        // For now, we log the keys that should be invalidated
        console.log(
          "📝 Additional cache keys to invalidate:",
          invalidationKeys
        );
      } catch (error) {
        console.warn("⚠️ Final loyalty cache invalidation failed:", error);
      }
    }

    // Dispatch final order completion action that will trigger middleware
    dispatch({ type: "checkout/completeOrderFlow" });

    // CRITICAL: Clear localStorage after successful sale
    clearCartFromStorage();
    console.log(
      "🗑️ Cart localStorage cleared after successful order completion"
    );

    // Clear selections when the entire flow is complete
    clearSelectedCustomer();
    clearSelectedAgent();

    // Reset completion data
    setCompletionOrderData(null);
    setShowOrderCompletionModal(false);

    // Reset pre-order check state
    setPrinterCheckResult(null);
    setPendingOrderAction(null);

    // Clear order data
    setLastOrderData(null);

    console.log("✅ Order completion finished");
  };

  // Load customers for selection
  const loadCustomers = useCallback(async (searchQuery = "") => {
    setIsLoadingCustomers(true);
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.getStoreCustomers({
        search: searchQuery,
        limit: 50,
        includeLoyalty: true, // Include loyalty data for customer selection
      });

      if (response.success && response.data) {
        setCustomers(response.data.customers || []);
      } else {
        console.error("Failed to load customers:", response.error);
        setCustomers([]);
      }
    } catch (error) {
      console.error("Error loading customers:", error);
      setCustomers([]);
    } finally {
      setIsLoadingCustomers(false);
    }
  }, []);

  // Load sales agents for selection
  const loadSalesAgents = useCallback(async (searchQuery = "") => {
    setIsLoadingSalesAgents(true);
    try {
      const apiClient = getAPIClient();
      const response = await apiClient.getSalesAgents();

      if (response.success && response.data) {
        let agents = response.data.salesAgents || [];

        // Filter by search query if provided
        if (searchQuery.trim()) {
          agents = agents.filter(
            (agent: any) =>
              agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
              agent.email.toLowerCase().includes(searchQuery.toLowerCase())
          );
        }

        setSalesAgents(agents);
      } else {
        console.error("Failed to load sales agents:", response.error);
        setSalesAgents([]);
      }
    } catch (error) {
      console.error("Error loading sales agents:", error);
      setSalesAgents([]);
    } finally {
      setIsLoadingSalesAgents(false);
    }
  }, []);

  // Extract numeric ID from Shopify GID
  const extractCustomerId = (customerId: string): string => {
    // Handle Shopify GID format: gid://shopify/Customer/7988938735753
    if (customerId.startsWith("gid://shopify/Customer/")) {
      return customerId.split("/").pop() || customerId;
    }
    return customerId;
  };

  // Enhanced loyalty processing with retry logic and comprehensive error handling
  const processLoyaltyPointsWithRetry = async (
    customer: any,
    paymentResult: any,
    shopifyResult: any,
    cartItems: any[],
    selectedAgent: any,
    user: any,
    selectedPaymentMethod: any,
    maxRetries: number = 3
  ) => {
    let loyaltyResult = null;
    let attempt = 0;

    while (attempt < maxRetries) {
      try {
        attempt++;
        console.log(
          `🔄 Processing loyalty points for order completion (Attempt ${attempt}/${maxRetries})...`
        );

        const orderCompletionData = {
          orderId: shopifyResult.shopifyOrderId || `ORDER_${Date.now()}`,
          orderNumber: shopifyResult.orderNumber || paymentResult.transactionId,
          customerId: customer.id,
          orderTotal: paymentResult.totalAmount,
          salesAgentId: selectedAgent?.id,
          staffId: user?.id || "",
          lineItems: cartItems.map((item) => ({
            id: item.variantId || item.productId,
            title: item.title,
            quantity: item.quantity,
            price:
              typeof item.price === "string"
                ? parseFloat(item.price)
                : item.price,
            sku: item.sku,
            variantId: item.variantId,
            productId: item.productId,
            // ✅ CRITICAL FIX: Include discount data for receipt generation
            discount: item.discount,
          })),
          paymentMethod: paymentResult.isSplitPayment
            ? "Split Payment"
            : selectedPaymentMethod?.method?.name || "Cash",
          transactionId: paymentResult.transactionId,
          loyaltyDiscount: selectedPaymentMethod?.metadata?.loyaltyDiscount,
        };

        console.log("📊 Loyalty processing data:", {
          customerId: customer.id,
          orderTotal: paymentResult.totalAmount,
          orderId: orderCompletionData.orderId,
          attempt: attempt,
        });

        loyaltyResult =
          await loyaltyOrderCompletionService.processLoyaltyCompletion(
            orderCompletionData
          );

        if (loyaltyResult.success) {
          console.log("✅ Loyalty points processed successfully:", {
            pointsAdded: loyaltyResult.pointsAdded,
            newBalance: loyaltyResult.newBalance,
            tierChanged: loyaltyResult.tierChanged,
            newTier: loyaltyResult.newTier,
            transactionId: loyaltyResult.transactionId,
          });

          // Invalidate loyalty caches for real-time updates
          invalidateLoyaltyCaches(customer.id);

          // Success - break out of retry loop
          break;
        } else {
          console.warn(
            `⚠️ Loyalty processing failed (Attempt ${attempt}):`,
            loyaltyResult.error
          );

          // If this was the last attempt, log the final failure
          if (attempt >= maxRetries) {
            console.error(
              `❌ Loyalty processing failed after ${maxRetries} attempts:`,
              loyaltyResult.error
            );

            // Store failed loyalty processing for manual retry
            await storeLoyaltyProcessingFailure(
              orderCompletionData,
              loyaltyResult.error
            );
          } else {
            // Wait before retrying (exponential backoff)
            await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
          }
        }
      } catch (error) {
        console.error(
          `❌ Error processing loyalty points (Attempt ${attempt}):`,
          error
        );

        // If this was the last attempt, log the final error
        if (attempt >= maxRetries) {
          console.error(
            `❌ Loyalty processing error after ${maxRetries} attempts:`,
            error
          );

          // Store failed loyalty processing for manual retry
          const orderCompletionData = {
            orderId: shopifyResult.shopifyOrderId || `ORDER_${Date.now()}`,
            orderNumber:
              shopifyResult.orderNumber || paymentResult.transactionId,
            customerId: customer.id,
            orderTotal: paymentResult.totalAmount,
            salesAgentId: selectedAgent?.id,
            staffId: user?.id || "",
          };

          await storeLoyaltyProcessingFailure(
            orderCompletionData,
            error instanceof Error ? error.message : "Unknown error"
          );
        } else {
          // Wait before retrying (exponential backoff)
          await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    return loyaltyResult;
  };

  // Store failed loyalty processing for manual retry
  const storeLoyaltyProcessingFailure = async (
    orderData: any,
    error: string
  ) => {
    try {
      console.log("💾 Storing failed loyalty processing for manual retry...");

      // Store in local storage for manual processing later
      const failedProcessing = {
        timestamp: new Date().toISOString(),
        orderData,
        error,
        customerId: orderData.customerId,
        orderTotal: orderData.orderTotal,
        orderId: orderData.orderId,
        retryCount: 0,
        status: "pending_retry",
      };

      // Get existing failed processings
      const existingFailures = await CrossPlatformStorage.getItemAsync(
        "failed_loyalty_processings"
      );
      const failures = existingFailures ? JSON.parse(existingFailures) : [];

      // Add new failure
      failures.push(failedProcessing);

      // Store updated failures
      await CrossPlatformStorage.setItemAsync(
        "failed_loyalty_processings",
        JSON.stringify(failures)
      );

      console.log("✅ Failed loyalty processing stored for manual retry");
    } catch (storageError) {
      console.error(
        "❌ Failed to store loyalty processing failure:",
        storageError
      );
    }
  };

  // Generate loyalty message for order completion
  const getLoyaltyMessage = (loyaltyResult: any, customer: any): string => {
    if (!customer) {
      return "";
    }

    if (loyaltyResult?.success) {
      const pointsMessage = `\n\n🎉 Loyalty Points: +${loyaltyResult.pointsAdded} points`;
      const tierMessage = loyaltyResult.tierChanged
        ? ` (Tier upgraded to ${loyaltyResult.newTier}!)`
        : "";
      const balanceMessage = `\n💰 New Balance: ${loyaltyResult.newBalance} points`;

      return `${pointsMessage}${tierMessage}${balanceMessage}`;
    } else if (loyaltyResult === null) {
      // Loyalty processing was attempted but failed
      return `\n\n⚠️ Loyalty Points: Processing failed - will retry automatically`;
    } else {
      // Loyalty processing failed with error
      return `\n\n⚠️ Loyalty Points: Processing failed - stored for manual retry`;
    }
  };

  // Load customer tickets
  const loadCustomerTickets = useCallback(async (customerId: string) => {
    setIsLoadingCustomerTickets(true);
    try {
      const apiClient = getAPIClient();
      // Extract numeric ID from Shopify GID
      const numericCustomerId = extractCustomerId(customerId);
      const response = await apiClient.getCustomerTickets(numericCustomerId, {
        status: "active,paused",
        limit: 10,
        includeExpired: false,
      });

      if (response.success && response.data) {
        return response.data.tickets || [];
      } else {
        console.error("Failed to load customer tickets:", response.error);
        return [];
      }
    } catch (error) {
      console.error("Error loading customer tickets:", error);
      return [];
    } finally {
      setIsLoadingCustomerTickets(false);
    }
  }, []);

  // Handle customer selection with ticket lookup
  const handleCustomerSelect = async (customer: Customer) => {
    // First, close the customer selection modal
    setShowCustomerModal(false);

    // Check for existing tickets
    const existingTickets = await loadCustomerTickets(customer.id);

    if (existingTickets.length > 0) {
      // Customer has existing tickets, show resume modal
      setCustomerTickets(existingTickets);
      setSelectedCustomer({
        id: customer.id,
        firstName: customer.firstName,
        lastName: customer.lastName || "",
        displayName:
          customer.displayName ||
          `${customer.firstName || ""} ${customer.lastName || ""}`.trim() ||
          "Unknown Customer",
        email: customer.email,
        phone: customer.phone,
      });
      setShowCustomerTicketResumeModal(true);
    } else {
      // No existing tickets, assign customer directly
      await assignCustomerToCurrentTicket(customer);
    }
  };

  // Assign customer to current ticket
  const assignCustomerToCurrentTicket = async (customer: Customer) => {
    // Fetch loyalty data for the customer
    let loyaltyData = null;
    try {
      const loyaltyService = (await import("@/src/services/loyalty-service"))
        .loyaltyService;
      loyaltyData = await loyaltyService.getCustomerLoyaltySummary(customer.id);
      console.log(
        "🔍 Fetched loyalty data for customer:",
        customer.id,
        loyaltyData
      );
    } catch (error) {
      console.error(
        "Failed to fetch loyalty data for customer:",
        customer.id,
        error
      );
    }

    setSelectedCustomer({
      id: customer.id,
      firstName: customer.firstName,
      lastName: customer.lastName || "",
      displayName:
        customer.displayName ||
        `${customer.firstName || ""} ${customer.lastName || ""}`.trim() ||
        "Unknown Customer",
      email: customer.email,
      phone: customer.phone,
      loyaltyData: loyaltyData, // Include loyalty data for receipt generation
    });

    // Update the active ticket with customer info
    dispatch(setActiveTicketCustomer(customer));
  };

  // Handle ticket resume actions
  const handleTicketResumeAction = async (action: TicketResumeAction) => {
    setIsLoadingCustomerTickets(true);
    try {
      switch (action.type) {
        case "resume":
          if (action.ticketId) {
            // Resume the selected ticket (replace current cart)
            dispatch(resumeCustomerTicket(action.ticketId));
            // Customer is already set from the action
            await assignCustomerToCurrentTicket(action.customer);
          }
          break;

        case "merge":
          if (action.ticketId) {
            // Merge the selected ticket with current cart
            dispatch(mergeCustomerTicket(action.ticketId));
            // Customer is already set from the action
            await assignCustomerToCurrentTicket(action.customer);
          }
          break;

        case "continue":
          // Continue with current cart, just assign customer
          await assignCustomerToCurrentTicket(action.customer);
          break;
      }

      // Close the resume modal
      setShowCustomerTicketResumeModal(false);
      setCustomerTickets([]);
    } catch (error) {
      console.error("Error handling ticket resume action:", error);
      setModalData({
        title: "Error",
        message: "Failed to process ticket action. Please try again.",
      });
      setShowErrorModal(true);
    } finally {
      setIsLoadingCustomerTickets(false);
    }
  };

  // Handle sales agent selection
  const handleSalesAgentSelect = (agent: any) => {
    setSelectedAgent({
      id: agent.id,
      name: agent.name,
      email: agent.email,
      territory: agent.territory || "",
      commissionRate: agent.commissionRate,
      active: agent.active,
    });
    setShowSalesAgentModal(false);
  };

  // Handle customer creation
  const handleCreateCustomer = async () => {
    if (
      !customerFormData.firstName.trim() ||
      !customerFormData.lastName.trim()
    ) {
      setModalData({
        title: "Validation Error",
        message: "First name and last name are required.",
      });
      setShowErrorModal(true);
      return;
    }

    setIsCreatingCustomer(true);
    try {
      const customerData = {
        firstName: customerFormData.firstName.trim(),
        lastName: customerFormData.lastName.trim(),
        email: customerFormData.email.trim() || undefined,
        phone: customerFormData.phone.trim() || undefined,
        note: "Created from POS checkout",
        tags: "pos,checkout",
      };

      const result = await dispatch(createCustomer(customerData)).unwrap();

      if (result) {
        // Auto-select the newly created customer (new customers have no loyalty data yet)
        setSelectedCustomer({
          id: result.id,
          firstName: result.firstName,
          lastName: result.lastName || "",
          displayName: `${result.firstName} ${result.lastName || ""}`.trim(),
          email: result.email,
          phone: result.phone,
          loyaltyData: null, // New customers start with no loyalty data
        });

        // Refresh customer list
        await loadCustomers(customerSearchQuery);

        // Close creation modal and customer selection modal
        setShowCustomerCreationModal(false);
        setShowCustomerModal(false);

        // Reset form
        setCustomerFormData({
          firstName: "",
          lastName: "",
          email: "",
          phone: "",
        });

        // Show success message
        setModalData({
          title: "Customer Created",
          message: `Customer "${result.firstName} ${result.lastName}" has been created and selected.`,
        });
        setShowSuccessModal(true);
      }
    } catch (error: any) {
      console.error("Customer creation error:", error);
      setModalData({
        title: "Customer Creation Failed",
        message:
          error.message || "Failed to create customer. Please try again.",
      });
      setShowErrorModal(true);
    } finally {
      setIsCreatingCustomer(false);
    }
  };

  // Load customers when modal opens
  useEffect(() => {
    if (showCustomerModal) {
      loadCustomers(customerSearchQuery);
    }
  }, [showCustomerModal, loadCustomers, customerSearchQuery]);

  // Load sales agents when modal opens
  useEffect(() => {
    if (showSalesAgentModal) {
      loadSalesAgents(agentSearchQuery);
    }
  }, [showSalesAgentModal, loadSalesAgents, agentSearchQuery]);

  // Set page title
  useEffect(() => {
    setCurrentTitle("Checkout");
  }, [setCurrentTitle]);

  // Handle success modal dismissal with proper cleanup
  const handleSuccessModalDismiss = async () => {
    console.log("🔄 Starting success modal dismissal with inventory update...");

    // Update inventory before clearing cart
    if (cartItems.length > 0) {
      const inventoryResult = await handlePostOrderInventoryUpdate(cartItems);
      if (inventoryResult.success) {
        console.log(
          "✅ Inventory updated successfully on success modal dismissal"
        );
      } else {
        console.error("❌ Inventory update failed:", inventoryResult.error);
        // Continue with dismissal even if inventory update fails
      }
    }

    // Complete ticket and clear selections when user dismisses success modal
    dispatch(completeActiveTicket());
    clearSelectedCustomer();
    clearSelectedAgent();

    // Reset pre-order check state
    setPrinterCheckResult(null);
    setPendingOrderAction(null);

    // Clear order data
    setLastOrderData(null);

    // Close modal
    setShowSuccessModal(false);

    console.log("✅ Success modal dismissal finished");
  };

  // Handle pre-order printer check result
  const handlePreOrderPrinterCheckResult = (
    result: PreOrderPrinterCheckResult
  ) => {
    setPrinterCheckResult(result);
    setShowPreOrderPrinterCheck(false);

    if (result.proceed) {
      // User chose to proceed with or without printer
      // Now execute the pending order action
      if (pendingOrderAction === "mpesa_till") {
        executeActualMpesaTillPayment(result);
      } else if (pendingOrderAction === "cash") {
        executeActualOrderPlacement(result);
      }
    } else {
      // User cancelled or chose to setup printer
      // Reset pending action
      setPendingOrderAction(null);
    }
  };

  // Execute the actual M-Pesa Till payment after printer check
  const executeActualMpesaTillPayment = async (
    printerInfo: PreOrderPrinterCheckResult
  ) => {
    if (!selectedCustomer || !selectedAgent || !user || !currentLocation) {
      setModalData({
        title: "Missing Information",
        message:
          "Missing required information for order creation. Please ensure customer, sales agent, and location are selected.",
      });
      setShowErrorModal(true);
      return;
    }

    setIsProcessing(true);
    try {
      const staffInfo = {
        id: user.id,
        name: user.name,
        terminal: "Current Terminal",
      };

      const customerInfo = {
        name:
          selectedCustomer.displayName ||
          `${selectedCustomer.firstName} ${selectedCustomer.lastName}`.trim() ||
          "Walk-in Customer",
        phone: selectedCustomer.phone || "",
      };

      const additionalData = {
        tillNumber: "123456", // Default ABSA till number
        accountNumber: customerInfo.phone || "POS-PAYMENT",
        amountTendered: cartTotal,
        transactionCode: transactionCode.trim(),
      };

      // Process ABSA Till payment
      const paymentResult = await PaymentService.processPayment(
        {
          method: PaymentService.getPaymentMethodById("absa_till")!,
          amount: cartTotal,
          currency: "KES",
        },
        staffInfo,
        customerInfo,
        additionalData
      );

      if (paymentResult.success) {
        // Route to modal workflow for order creation
        setModalData({
          title: "Payment Successful",
          message:
            "M-Pesa payment processed successfully. Please proceed to complete your order.",
        });
        setShowSuccessModal(true);
        // Note: Order creation will happen through the modal workflow
      } else {
        setModalData({
          title: "Payment Failed",
          message:
            paymentResult.error ||
            "M-Pesa Till payment processing failed. Please try again.",
        });
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error("M-Pesa Till payment processing error:", error);
      setModalData({
        title: "Payment Error",
        message:
          "Payment processing failed. Please check your connection and try again.",
      });
      setShowErrorModal(true);
    } finally {
      setIsProcessing(false);
    }
  };

  // Execute the actual order placement after printer check
  const executeActualOrderPlacement = async (
    printerInfo: PreOrderPrinterCheckResult
  ): Promise<{
    success: boolean;
    error?: string;
    orderData?: any;
    orderNumber?: string;
  }> => {
    setIsProcessing(true);
    try {
      // Import the enhanced payment service
      const { enhancedPaymentService } = await import(
        "@/src/services/enhanced-payment-service"
      );

      // Check if this is a split payment
      const isSplitPayment = selectedPaymentMethod?.method?.type === "split";

      // Step 1: Initiate payment transaction
      const transactionRequest = {
        totalAmount: finalOrderTotal,
        currency: "KES",
        customerId: selectedCustomer!.id,
        staffId: user?.id,
        terminalId: "TERMINAL_001",
        locationId: currentLocation?.id,
        paymentMethods: [], // Don't add payment methods during initiation - add them in processing phase
        metadata: {
          customerName:
            selectedCustomer?.displayName || selectedCustomer?.firstName,
          isSplitPayment,
          orderData: {
            line_items: displayCartItems.map((item) => ({
              variant_id: item.variantId,
              quantity: item.quantity,
              price: item.price.toString(),
              title: item.title,
              sku: item.sku,
            })),
            customer: {
              id: selectedCustomer!.id,
              first_name: selectedCustomer!.firstName,
              last_name: selectedCustomer!.lastName,
              email: selectedCustomer!.email,
              phone: selectedCustomer!.phone,
            },
          },
        },
      };

      const transactionResult =
        await enhancedPaymentService.initiateTransaction(transactionRequest);

      if (!transactionResult.success) {
        throw new Error(
          transactionResult.error || "Failed to initiate transaction"
        );
      }

      const transactionId = transactionResult.data!.transactionId;

      // Step 2 & 3: Add and process payment methods
      if (isSplitPayment) {
        // Handle split payment - process each method
        const splitMethods = selectedPaymentMethod.metadata?.splitConfig || [];

        for (const splitMethod of splitMethods) {
          // Add payment method
          const methodResult = await enhancedPaymentService.addPaymentMethod(
            transactionId,
            {
              methodType: splitMethod.method.type,
              methodName: splitMethod.method.name,
              amount: splitMethod.amount,
              metadata: splitMethod.config || {},
            }
          );

          if (!methodResult.success) {
            throw new Error(
              `Failed to add payment method ${splitMethod.method.name}: ${methodResult.error}`
            );
          }

          const methodId = methodResult.data!.methodId;

          // Process the payment method based on type
          let processingData: any = {};

          switch (splitMethod.method.type) {
            case "cash":
              processingData = {
                amountTendered:
                  splitMethod.config?.amountTendered || splitMethod.amount,
              };
              break;
            case "mpesa":
              processingData = splitMethod.config?.stkPushSent
                ? {
                    paymentMethod: "stk_push",
                    phoneNumber: selectedCustomer!.phone || "************",
                    accountReference: `ORDER_${transactionId}`,
                    transactionDesc: `Payment for order ${transactionId}`,
                  }
                : {
                    paymentMethod: "manual_code",
                    transactionCode: splitMethod.config?.transactionCode || "",
                  };
              break;
            case "absa_till":
              processingData = {
                transactionCode: splitMethod.config?.transactionCode || "",
                tillNumber: "123456",
              };
              break;
            case "credit":
              processingData = {
                customerName:
                  selectedCustomer?.displayName ||
                  selectedCustomer?.firstName ||
                  "Unknown Customer",
                customerPhone: selectedCustomer!.phone || "************",
                creditLimit: 50000,
                dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                  .toISOString()
                  .split("T")[0],
              };
              break;
            default:
              processingData = {};
          }

          const processResult =
            await enhancedPaymentService.processPaymentMethod(
              methodId,
              processingData
            );

          if (!processResult.success) {
            throw new Error(
              `Payment processing failed for ${splitMethod.method.name}: ${processResult.error}`
            );
          }
        }
      } else if (selectedPaymentMethod) {
        // Handle single payment method
        const methodResult = await enhancedPaymentService.addPaymentMethod(
          transactionId,
          {
            methodType: selectedPaymentMethod.method.type,
            methodName: selectedPaymentMethod.method.name,
            amount: selectedPaymentMethod.amount,
            metadata: selectedPaymentMethod.metadata || {},
          }
        );

        if (!methodResult.success) {
          throw new Error(
            `Failed to add payment method: ${methodResult.error}`
          );
        }

        const methodId = methodResult.data!.methodId;

        // Process the payment method based on type
        let processingData: any = {};

        switch (selectedPaymentMethod.method.type) {
          case "cash":
            processingData = {
              amountTendered: selectedPaymentMethod.amount,
            };
            break;
          case "mpesa":
            // Determine M-Pesa payment method based on user selection
            const mpesaMetadata = selectedPaymentMethod.metadata || {};
            const isStkPush = mpesaMetadata.stkPushSent === true;
            const hasTransactionCode =
              mpesaMetadata.transactionCode &&
              mpesaMetadata.transactionCode.length > 0;

            // Extract customer phone number with comprehensive fallbacks
            const customerPhone =
              mpesaMetadata.customerPhone || // From PaymentFlowManager phone input
              mpesaMetadata.phoneNumber || // Legacy fallback
              selectedCustomer?.phone || // From selected customer
              selectedCustomer?.phoneNumber || // Alternative customer field
              "************"; // Default fallback

            console.log(
              `📱 M-Pesa payment processing - Phone: ${customerPhone}, STK Push: ${isStkPush}, Transaction Code: ${
                mpesaMetadata.transactionCode || "None"
              }`
            );

            if (isStkPush) {
              // STK Push payment
              processingData = {
                paymentMethod: "stk_push",
                phoneNumber: customerPhone,
                accountReference: `ORDER_${transactionId}`,
                transactionDesc: `Payment for order ${transactionId}`,
              };
            } else if (hasTransactionCode) {
              // Manual transaction code
              processingData = {
                paymentMethod: "manual_code",
                phoneNumber: customerPhone,
                transactionCode: mpesaMetadata.transactionCode,
                accountReference: `ORDER_${transactionId}`,
                transactionDesc: `Payment for order ${transactionId}`,
              };
            } else {
              // Fallback to manual code with empty transaction code (will be validated by backend)
              processingData = {
                paymentMethod: "manual_code",
                phoneNumber: customerPhone,
                transactionCode: "",
                accountReference: `ORDER_${transactionId}`,
                transactionDesc: `Payment for order ${transactionId}`,
              };
            }
            break;
          case "absa_till":
            // Extract customer information for ABSA Till payments
            const absaCustomerName =
              selectedPaymentMethod.metadata?.customerName ||
              selectedCustomer?.displayName ||
              selectedCustomer?.firstName ||
              "Unknown Customer";

            const absaCustomerPhone =
              selectedPaymentMethod.metadata?.customerPhone ||
              selectedCustomer?.phone ||
              "************";

            processingData = {
              transactionCode:
                selectedPaymentMethod.metadata?.transactionCode || "DEMO123456",
              tillNumber: "123456",
              customerName: absaCustomerName,
              customerPhone: absaCustomerPhone,
            };
            break;
          case "credit":
            // Extract customer information with fallbacks
            const creditCustomerName =
              selectedPaymentMethod.metadata?.customerName ||
              selectedCustomer?.displayName ||
              selectedCustomer?.firstName ||
              "Unknown Customer";

            const creditCustomerPhone =
              selectedPaymentMethod.metadata?.customerPhone ||
              selectedCustomer?.phone ||
              "************";

            processingData = {
              customerId: selectedCustomer?.id || null,
              customerName: creditCustomerName,
              customerPhone: creditCustomerPhone,
              creditLimit: 50000,
              dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                .toISOString()
                .split("T")[0],
              paymentTerms: "Net 30",
              notes: `POS Credit Payment - Staff: ${user?.name}`,
              existingBalance: 0,
            };
            break;
          default:
            processingData = {};
        }

        const processResult = await enhancedPaymentService.processPaymentMethod(
          methodId,
          processingData
        );

        if (!processResult.success) {
          throw new Error(`Payment processing failed: ${processResult.error}`);
        }
      } else {
        // Default cash payment - only if no payment method was selected
        console.log(
          "⚠️ No payment method selected, defaulting to cash payment"
        );

        const methodResult = await enhancedPaymentService.addPaymentMethod(
          transactionId,
          {
            methodType: "cash",
            methodName: "Cash",
            amount: displayCartTotal,
            metadata: {},
          }
        );

        if (!methodResult.success) {
          throw new Error(
            `Failed to add default cash payment method: ${methodResult.error}`
          );
        }

        const methodId = methodResult.data!.methodId;

        const processResult = await enhancedPaymentService.processPaymentMethod(
          methodId,
          {
            amountTendered: displayCartTotal,
          }
        );

        if (!processResult.success) {
          throw new Error(
            `Default cash payment processing failed: ${processResult.error}`
          );
        }

        console.log("✅ Default cash payment processed successfully");
      }

      // Step 4: Create Shopify order
      const orderData = {
        line_items: displayCartItems.map((item) => ({
          variant_id: item.variantId,
          quantity: item.quantity,
          price: item.price.toString(),
          title: item.title,
          sku: item.sku,
        })),
        customer: {
          id: selectedCustomer!.id,
          first_name: selectedCustomer!.firstName,
          last_name: selectedCustomer!.lastName,
          email: selectedCustomer!.email,
          phone: selectedCustomer!.phone,
        },
        financial_status: "pending",
        fulfillment_status: null,
        note: `POS Order - Staff: ${user?.name}, Agent: ${selectedAgent?.name}${
          shippingData?.includeShipping
            ? `, Shipping: ${shippingData.deliveryMethod}`
            : ""
        }`,
        note_attributes: [
          { name: "pos_staff_id", value: user?.id || "" },
          { name: "sales_agent_id", value: selectedAgent?.id || "" },
          { name: "location_id", value: currentLocation?.id || "" },
          { name: "payment_transaction_id", value: transactionId },
          {
            name: "payment_method",
            value: isSplitPayment
              ? "Split Payment"
              : selectedPaymentMethod?.method?.name || "Cash",
          },
          ...(shippingData?.includeShipping
            ? [
                {
                  name: "shipping_method",
                  value: shippingData.deliveryMethod || "",
                },
                {
                  name: "shipping_fee",
                  value: shippingData.shippingFee?.toString() || "0",
                },
                {
                  name: "delivery_address",
                  value: shippingData.deliveryAddress
                    ? `${shippingData.deliveryAddress.address1}, ${shippingData.deliveryAddress.city}`
                    : "",
                },
              ]
            : []),
          ...(isSplitPayment
            ? [
                {
                  name: "split_payment_methods",
                  value:
                    selectedPaymentMethod.metadata?.splitConfig
                      ?.map((m: any) => m.method.name)
                      .join(", ") || "",
                },
                {
                  name: "split_payment_count",
                  value:
                    selectedPaymentMethod.metadata?.splitConfig?.length?.toString() ||
                    "0",
                },
              ]
            : []),
        ],
        tags: [
          "pos",
          "enhanced-payment",
          ...(isSplitPayment ? ["split-payment"] : []),
          ...(shippingData?.includeShipping ? ["shipping"] : []),
        ].join(","),
        // Add shipping lines if shipping is included
        ...(shippingData.fee > 0 && shippingData.data
          ? {
              shipping_lines: [
                {
                  title:
                    shippingData.data.deliveryMethod || "Standard Delivery",
                  price: shippingData.fee.toString(),
                  code:
                    shippingData.data.deliveryMethod
                      ?.toLowerCase()
                      .replace(/\s+/g, "_") || "standard",
                },
              ],
            }
          : {}),
      };

      const shopifyResult =
        await enhancedPaymentService.completeTransactionWithShopify(
          transactionId,
          orderData
        );

      if (shopifyResult.success) {
        console.log("✅ Shopify order created successfully:", shopifyResult);
        console.log("🖨️ Printer info:", printerInfo);
        console.log("🖨️ Printer available:", printerInfo.printerAvailable);

        // Create order data for receipt
        const orderDataForCompletion = {
          id: shopifyResult.shopifyOrderId || `ORDER_${Date.now()}`,
          orderNumber: shopifyResult.orderNumber || transactionId,
          totalPrice: finalOrderTotal.toString(), // Include shipping fee in total
          createdAt: new Date().toISOString(),
          salespersonName: user!.name,
          salespersonId: selectedAgent?.id || "",
          paymentMethod: isSplitPayment
            ? "Split Payment"
            : selectedPaymentMethod?.method?.name || "Cash",
          splitPaymentData: isSplitPayment
            ? selectedPaymentMethod.metadata?.splitConfig
            : undefined,
          customer: {
            id: selectedCustomer!.id,
            firstName: selectedCustomer!.firstName,
            lastName: selectedCustomer!.lastName,
            email: selectedCustomer!.email,
            phone: selectedCustomer!.phone,
            loyaltyData: selectedCustomer.loyaltyData, // Include loyalty data for receipt
          },
          lineItems: displayCartItems.map((item) => ({
            id: item.variantId || item.productId,
            title: item.title,
            quantity: item.quantity,
            price: item.price,
            sku: item.sku,
            variantId: item.variantId,
            productId: item.productId,
            // ✅ CRITICAL FIX: Include discount data for receipt generation
            discount: item.discount,
          })),
          // Include shipping information for receipt generation
          shippingData: shippingData,
          shipping_lines:
            shippingData.fee > 0 && shippingData.data
              ? [
                  {
                    title:
                      shippingData.data.deliveryMethod || "Standard Delivery",
                    price: shippingData.fee.toString(),
                    code:
                      shippingData.data.deliveryMethod
                        ?.toLowerCase()
                        .replace(/\s+/g, "_") || "standard",
                  },
                ]
              : [],
          loyaltyDiscount: selectedPaymentMethod?.metadata?.loyaltyDiscount, // Include any applied loyalty discount
        };

        console.log("🔍 Order data for receipt generation:", {
          customerId: selectedCustomer!.id,
          customerLoyaltyData: selectedCustomer.loyaltyData,
          shippingData: shippingData,
          loyaltyDiscount: selectedPaymentMethod?.metadata?.loyaltyDiscount,
        });

        // Store order data for manual receipt printing (will be updated with loyalty data later)
        setLastOrderData(orderDataForCompletion);

        // Update inventory immediately after successful order creation
        const inventoryResult = await handlePostOrderInventoryUpdate(
          displayCartItems
        );
        if (inventoryResult.success) {
          console.log(
            "✅ Inventory updated successfully after order placement"
          );
        } else {
          console.error("❌ Inventory update failed:", inventoryResult.error);
        }

        const orderNumber = shopifyResult.orderNumber || transactionId;

        // Handle printing based on printer availability using cross-platform service
        if (printerInfo.printerAvailable) {
          // Printer is available, attempt automatic printing with cross-platform service
          try {
            console.log("🖨️ Starting automatic printing process...");

            // Prepare order data for unified receipt printing
            let orderDataWithLoyalty = { ...orderDataForCompletion };

            // Use UnifiedReceiptManager for consistent receipt generation
            console.log("🖨️ Attempting unified receipt printing...");

            // Add loyalty data to order if available
            if (loyaltyResult?.success) {
              orderDataWithLoyalty.loyaltyCompletion = loyaltyResult;
            }

            const unifiedResult = await UnifiedReceiptManager.generateReceipt(
              orderDataWithLoyalty,
              {
                format: "thermal",
                autoPrint: true,
                printerType: "thermal",
              }
            );
            console.log("🖨️ Unified printing result:", unifiedResult);

            if (unifiedResult.success && unifiedResult.printed) {
              // Unified printing successful
              console.log("🖨️ Unified printing successful");
              setPendingOrderAction(null);
              setPrinterCheckResult(null);

              setModalData({
                title: "Order Completed Successfully!",
                message: `Order #${orderNumber} has been created and receipt printed successfully.\n\nTotal: KSh ${finalOrderTotal.toFixed(
                  2
                )}\nPayment: ${
                  isSplitPayment
                    ? "Split Payment"
                    : selectedPaymentMethod?.method?.name || "Cash"
                }`,
                orderNumber,
                orderTotal: finalOrderTotal,
              });
              setShowSuccessModal(true);
              return;
            }

            // Unified printing failed, show completion modal for manual retry
            console.log("🖨️ Unified printing failed:", unifiedResult.error);

            setCompletionOrderData({
              orderData: orderDataForCompletion,
              orderNumber,
              orderTotal: finalOrderTotal,
              paymentMethod: isSplitPayment
                ? "Split Payment"
                : selectedPaymentMethod?.method?.name || "Cash",
              transactionId: transactionId,
              printingAlreadyAttempted: true,
              printingWasSuccessful: false,
            });
            setShowOrderCompletionModal(true);
          } catch (error) {
            console.error("🖨️ Automatic printing error:", error);
            console.error("🖨️ Error details:", {
              message: error.message,
              stack: error.stack,
              name: error.name,
            });
            console.log("🖨️ Setting completion order data for error case");
            // Printing failed, show completion modal for retry options
            const completionData = {
              orderData: orderDataForCompletion,
              orderNumber,
              orderTotal: finalOrderTotal,
              paymentMethod: isSplitPayment
                ? "Split Payment"
                : selectedPaymentMethod?.method?.name || "Cash",
              transactionId: transactionId,
              printingAlreadyAttempted: true,
              printingWasSuccessful: false,
            };
            console.log("Completion data:", completionData);
            setCompletionOrderData(completionData);
            setShowOrderCompletionModal(true);
            console.log("OrderCompletionModal should now be visible");
          }
        } else {
          // No printer available, show success modal but preserve data for receipt operations
          // Data will be cleared when user dismisses the success modal
          setPendingOrderAction(null);
          setPrinterCheckResult(null);

          setModalData({
            title: "Order Completed Successfully!",
            message: `Order #${orderNumber} has been created.\n\nTotal: KSh ${finalOrderTotal.toFixed(
              2
            )}\nPayment: ${
              isSplitPayment
                ? "Split Payment"
                : selectedPaymentMethod?.method?.name || "Cash"
            }\n\nReceipt can be printed later from Orders screen.`,
            orderNumber,
            orderTotal: finalOrderTotal,
          });
          setShowSuccessModal(true);
        }

        // Return success result
        return {
          success: true,
          orderData: orderDataForCompletion,
          orderNumber: orderNumber,
        };
      } else {
        throw new Error(
          shopifyResult.error || "Failed to create Shopify order"
        );
      }
    } catch (error) {
      console.error("❌ Failed to place order:", error);
      console.error("❌ Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name,
      });
      setModalData({
        title: "Error",
        message: `Failed to place order: ${
          error.message || "Unknown error"
        }. Please try again.`,
      });
      setShowErrorModal(true);

      // Return error result
      return {
        success: false,
        error: error.message || "Unknown error",
      };
    } finally {
      setIsProcessing(false);
    }
  };

  // Theme system
  const theme = useTheme();
  const responsiveLayout = useResponsiveLayout();
  const backgroundColor = useThemeColor({}, "background");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const successColor = useThemeColor({}, "success");
  const borderColor = useThemeColor({}, "border");

  // Create styles using theme
  const styles = createStyles(theme, responsiveLayout);

  // Old handleManualReceiptPrint removed - functionality integrated into handlePrintReceiptFromSuccess

  // Enhanced action handlers that preserve data for receipt operations
  const handleViewOrdersFromSuccess = () => {
    router.replace("/(tabs)/orders");
    handleSuccessModalDismiss();
  };

  const handleNewSaleFromSuccess = () => {
    router.replace("/(tabs)/products");
    handleSuccessModalDismiss();
  };

  const handleViewReceiptFromSuccess = () => {
    if (!modalData.orderNumber) {
      setModalData({
        title: "Error",
        message: "Order number not available for receipt viewing.",
      });
      setShowErrorModal(true);
      return;
    }
    router.push(`/order-receipt?orderNumber=${modalData.orderNumber}`);
    handleSuccessModalDismiss();
  };

  const handlePrintReceiptFromSuccess = async () => {
    if (!lastOrderData) {
      setModalData({
        title: "No Order Data",
        message: "No order data available for printing.",
      });
      setShowErrorModal(true);
      return;
    }

    try {
      // Prepare loyalty completion data for unified receipt printing
      let orderDataWithLoyalty = { ...lastOrderData };
      if (
        lastOrderData?.loyaltyCompletion?.success &&
        lastOrderData?.customer
      ) {
        // Loyalty data is already in the order data from previous processing
        console.log(
          "🎯 Using existing loyalty completion data for manual receipt:",
          orderDataWithLoyalty.loyaltyCompletion
        );
      }

      // Try unified receipt printing with automatic fallback
      const unifiedResult = await UnifiedReceiptManager.generateReceipt(
        orderDataWithLoyalty,
        {
          format: "thermal",
          autoPrint: true,
          printerType: "thermal",
        }
      );

      if (unifiedResult.success && unifiedResult.printed) {
        // Unified thermal printing successful
        setModalData({
          title: "Order Completed Successfully!",
          message: `${modalData.message}\n\nReceipt has been printed successfully using thermal printer!`,
          orderNumber: modalData.orderNumber,
          orderTotal: modalData.orderTotal,
        });
        return;
      }

      // Unified printing failed
      console.log("🖨️ Unified printing failed:", unifiedResult.error);

      setModalData({
        title: "Print Error",
        message: `Failed to print receipt: ${
          unifiedResult.error || "Unknown error"
        }\n\nPlease check your printer connection and try again.`,
      });
      setShowErrorModal(true);
    } catch (error) {
      console.error("Receipt printing error:", error);
      setModalData({
        title: "Print Error",
        message:
          "Failed to print receipt. Please check your printer connection.",
      });
      setShowErrorModal(true);
    }
  };

  // Old thermal printing logic removed - now handled by pre-order printer check

  // Handle order placement within the unified modal - using enhanced payment flow for proper payment tracking
  const handlePlaceOrderInModal = async () => {
    try {
      // Determine payment method details
      const isSplitPayment = selectedPaymentMethod?.method?.type === "split";
      const paymentMethodName = isSplitPayment
        ? "Split Payment"
        : selectedPaymentMethod?.method?.name || "Cash";

      // Step 1: Initiate payment transaction
      const transactionRequest = {
        totalAmount: finalOrderTotal, // ✅ FIXED: Use finalOrderTotal (includes shipping)
        currency: "KES",
        customerId: selectedCustomer!.id,
        staffId: user?.id,
        terminalId: "TERMINAL_001",
        locationId: currentLocation?.id,
        paymentMethods: [], // Add payment methods in processing phase
        metadata: {
          customerName:
            selectedCustomer?.displayName || selectedCustomer?.firstName,
          isSplitPayment,
          modalFlow: true, // Flag to indicate this is from modal workflow
          shippingIncluded: shippingData.fee > 0,
          shippingFee: shippingData.fee || 0,
        },
      };

      const transactionResult =
        await enhancedPaymentService.initiateTransaction(transactionRequest);

      if (!transactionResult.success) {
        throw new Error(
          transactionResult.error || "Failed to initiate transaction"
        );
      }

      const transactionId = transactionResult.data!.transactionId;

      // Step 2 & 3: Add and process payment methods
      if (isSplitPayment) {
        // Handle split payment - process each method separately
        const splitMethods = selectedPaymentMethod.metadata?.splitConfig || [];

        for (const splitMethod of splitMethods) {
          // Add payment method
          const methodResult = await enhancedPaymentService.addPaymentMethod(
            transactionId,
            {
              methodType: splitMethod.method.type,
              methodName: splitMethod.method.name,
              amount: splitMethod.amount,
              metadata: {
                ...splitMethod.config,
                modalFlow: true,
                customerName:
                  selectedCustomer?.displayName || selectedCustomer?.firstName,
                customerPhone: selectedCustomer?.phone,
                customerEmail: selectedCustomer?.email,
              },
            }
          );

          if (!methodResult.success) {
            throw new Error(
              `Failed to add ${splitMethod.method.name} payment method: ${methodResult.error}`
            );
          }

          const methodId = methodResult.data!.methodId;

          // Process the payment method with appropriate data
          let processingData: any = {
            modalFlow: true,
          };

          switch (splitMethod.method.type) {
            case "cash":
              processingData.amountTendered = splitMethod.amount;
              break;
            case "credit":
              processingData = {
                ...processingData,
                customerId: selectedCustomer?.id || null,
                customerName:
                  selectedCustomer?.displayName ||
                  selectedCustomer?.firstName ||
                  "Unknown Customer",
                customerPhone: selectedCustomer?.phone || "************",
                creditLimit: 50000,
                dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                  .toISOString()
                  .split("T")[0],
                paymentTerms: "Net 30",
                notes: `POS Credit Payment - Staff: ${user?.name}`,
                existingBalance: 0,
              };
              break;
            case "mpesa":
              processingData = {
                ...processingData,
                paymentMethod: "manual_code",
                phoneNumber: selectedCustomer?.phone || "************",
                transactionCode: splitMethod.config?.transactionCode || "",
                accountReference: `ORDER_${transactionId}`,
                transactionDesc: `Payment for order ${transactionId}`,
              };
              break;
            case "absa_till":
              processingData = {
                ...processingData,
                transactionCode:
                  splitMethod.config?.transactionCode || "DEMO123456",
                tillNumber: "123456",
                customerName:
                  selectedCustomer?.displayName ||
                  selectedCustomer?.firstName ||
                  "Unknown Customer",
                customerPhone: selectedCustomer?.phone || "************",
              };
              break;
            default:
              processingData.amountTendered = splitMethod.amount;
          }

          const processResult =
            await enhancedPaymentService.processPaymentMethod(
              methodId,
              processingData
            );

          if (!processResult.success) {
            throw new Error(
              `${splitMethod.method.name} payment processing failed: ${processResult.error}`
            );
          }
        }
      } else {
        // Handle single payment method
        const methodResult = await enhancedPaymentService.addPaymentMethod(
          transactionId,
          {
            methodType: selectedPaymentMethod?.method?.type || "cash",
            methodName: paymentMethodName,
            amount: finalOrderTotal, // ✅ FIXED: Use finalOrderTotal (includes shipping)
            metadata: selectedPaymentMethod?.metadata || {},
          }
        );

        if (!methodResult.success) {
          throw new Error(
            `Failed to add payment method: ${methodResult.error}`
          );
        }

        const methodId = methodResult.data!.methodId;

        // Step 3: Process the single payment method with proper data based on method type
        let processingData: any = {
          modalFlow: true,
        };

        // Set processing data based on payment method type
        const methodType = selectedPaymentMethod?.method?.type || "cash";
        const metadata = selectedPaymentMethod?.metadata || {};

        switch (methodType) {
          case "cash":
            processingData.amountTendered = finalOrderTotal; // ✅ FIXED: Use finalOrderTotal (includes shipping)
            break;
          case "mpesa":
            // Determine M-Pesa payment method based on user selection
            const isStkPush = metadata.stkPushSent === true;
            const hasTransactionCode =
              metadata.transactionCode && metadata.transactionCode.length > 0;

            // Extract customer phone number with comprehensive fallbacks
            const customerPhone =
              metadata.customerPhone || // From PaymentFlowManager phone input
              metadata.phoneNumber || // Legacy fallback
              selectedCustomer?.phone || // From selected customer
              selectedCustomer?.phoneNumber || // Alternative customer field
              "************"; // Default fallback

            console.log(
              `📱 M-Pesa modal payment processing - Phone: ${customerPhone}, STK Push: ${isStkPush}, Transaction Code: ${
                metadata.transactionCode || "None"
              }`
            );

            if (isStkPush) {
              processingData = {
                ...processingData,
                paymentMethod: "stk_push",
                phoneNumber: customerPhone,
                accountReference: `ORDER_${transactionId}`,
                transactionDesc: `Payment for order ${transactionId}`,
              };
            } else if (hasTransactionCode) {
              processingData = {
                ...processingData,
                paymentMethod: "manual_code",
                phoneNumber: customerPhone,
                transactionCode: metadata.transactionCode,
                accountReference: `ORDER_${transactionId}`,
                transactionDesc: `Payment for order ${transactionId}`,
              };
            } else {
              processingData = {
                ...processingData,
                paymentMethod: "manual_code",
                phoneNumber: customerPhone,
                transactionCode: "",
                accountReference: `ORDER_${transactionId}`,
                transactionDesc: `Payment for order ${transactionId}`,
              };
            }
            break;
          case "absa_till":
            // Extract customer information for ABSA Till payments
            const modalAbsaCustomerName =
              metadata.customerName ||
              selectedCustomer?.displayName ||
              selectedCustomer?.firstName ||
              "Unknown Customer";

            const modalAbsaCustomerPhone =
              metadata.customerPhone ||
              selectedCustomer?.phone ||
              "************";

            processingData = {
              ...processingData,
              transactionCode: metadata.transactionCode || "DEMO123456",
              tillNumber: "123456",
              customerName: modalAbsaCustomerName,
              customerPhone: modalAbsaCustomerPhone,
            };
            break;
          case "credit":
            // Extract customer information with fallbacks
            const modalCreditCustomerName =
              metadata.customerName ||
              selectedCustomer?.displayName ||
              selectedCustomer?.firstName ||
              "Unknown Customer";

            const modalCreditCustomerPhone =
              metadata.customerPhone ||
              selectedCustomer?.phone ||
              "************";

            processingData = {
              ...processingData,
              customerId: selectedCustomer?.id || null,
              customerName: modalCreditCustomerName,
              customerPhone: modalCreditCustomerPhone,
              creditLimit: 50000,
              dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                .toISOString()
                .split("T")[0],
              paymentTerms: "Net 30",
              notes: `POS Credit Payment - Staff: ${user?.name}`,
              existingBalance: 0,
            };
            break;
          default:
            processingData.amountTendered = finalOrderTotal; // ✅ FIXED: Use finalOrderTotal (includes shipping)
        }

        const processResult = await enhancedPaymentService.processPaymentMethod(
          methodId,
          processingData
        );

        if (!processResult.success) {
          throw new Error(`Payment processing failed: ${processResult.error}`);
        }
      }

      // Step 4: Create Shopify order with payment integration
      const orderData = {
        line_items: displayCartItems.map((item) => ({
          variant_id: item.variantId,
          quantity: item.quantity,
          price: item.price.toString(),
          title: item.title,
          sku: item.sku,
        })),
        customer: {
          id: selectedCustomer!.id,
          first_name: selectedCustomer!.firstName,
          last_name: selectedCustomer!.lastName,
          email: selectedCustomer!.email,
          phone: selectedCustomer!.phone,
        },
        financial_status: "pending",
        fulfillment_status: null,
        note: `POS Order - Staff: ${user?.name}, Agent: ${selectedAgent?.name}`,
        note_attributes: [
          { name: "pos_staff_id", value: user?.id || "" },
          { name: "sales_agent_id", value: selectedAgent?.id || "" },
          { name: "location_id", value: currentLocation?.id || "" },
          { name: "payment_transaction_id", value: transactionId },
          { name: "payment_method", value: paymentMethodName },
          { name: "modal_workflow", value: "true" },
          // ✅ FIXED: Add shipping data to note_attributes
          ...(shippingData?.includeShipping
            ? [
                {
                  name: "shipping_method",
                  value: shippingData.deliveryMethod || "",
                },
                {
                  name: "shipping_fee",
                  value: shippingData.shippingFee?.toString() || "0",
                },
                {
                  name: "delivery_address",
                  value: shippingData.deliveryAddress
                    ? `${shippingData.deliveryAddress.address1}, ${shippingData.deliveryAddress.city}`
                    : "",
                },
              ]
            : []),
        ],
        tags: [
          "pos",
          "enhanced-payment",
          "modal-workflow",
          ...(shippingData.fee > 0 ? ["shipping"] : []),
        ].join(","),
        // ✅ FIXED: Add shipping_lines if shipping is included
        ...(shippingData.fee > 0 && shippingData.data
          ? {
              shipping_lines: [
                {
                  title:
                    shippingData.data.deliveryMethod || "Standard Delivery",
                  price: shippingData.fee.toString(),
                  code:
                    shippingData.data.deliveryMethod
                      ?.toLowerCase()
                      .replace(/\s+/g, "_") || "standard",
                },
              ],
            }
          : {}),
      };

      const shopifyResult =
        await enhancedPaymentService.completeTransactionWithShopify(
          transactionId,
          orderData
        );

      if (shopifyResult.success) {
        // ✅ FIXED: Process loyalty points for modal flow (was missing!)
        let loyaltyResult = shopifyResult.loyaltyResult || null;

        // If backend didn't process loyalty (fallback), process manually
        if (selectedCustomer && !loyaltyResult?.success) {
          console.log(
            "⚠️ Modal flow: Backend didn't process loyalty points, falling back to manual processing..."
          );

          try {
            const { loyaltyOrderCompletionService } = await import(
              "@/src/services/loyalty-order-completion"
            );

            const orderCompletionData = {
              customerId: selectedCustomer.id,
              orderTotal: finalOrderTotal, // Use finalOrderTotal (includes shipping)
              orderId: shopifyResult.shopifyOrderId || `ORDER_${Date.now()}`,
              orderNumber: shopifyResult.orderNumber || transactionId,
              staffId: user?.id || "unknown",
              salesAgentId: selectedAgent?.id,
              paymentMethod: paymentMethodName,
              transactionId: transactionId,
              loyaltyDiscount: selectedPaymentMethod?.metadata?.loyaltyDiscount,
              lineItems: displayCartItems.map((item) => ({
                id: item.variantId,
                title: item.title,
                quantity: item.quantity,
                price: parseFloat(item.price.toString()), // ✅ FIXED: Convert to number
                sku: item.sku,
                variantId: item.variantId,
                productId: item.productId,
                // ✅ CRITICAL FIX: Include discount data for receipt generation
                discount: item.discount,
              })),
            };

            console.log("📊 Modal flow loyalty processing data:", {
              customerId: selectedCustomer.id,
              orderTotal: finalOrderTotal,
              orderId: shopifyResult.shopifyOrderId,
            });

            loyaltyResult =
              await loyaltyOrderCompletionService.processLoyaltyCompletion(
                orderCompletionData
              );

            if (loyaltyResult.success) {
              console.log(
                "✅ Modal flow loyalty points processed successfully:",
                {
                  pointsAdded: loyaltyResult.pointsAdded,
                  newBalance: loyaltyResult.newBalance,
                  tierChanged: loyaltyResult.tierChanged,
                  newTier: loyaltyResult.newTier,
                }
              );

              // Invalidate loyalty caches for real-time updates
              const { invalidateLoyaltyCaches } = await import(
                "@/src/services/loyalty-order-completion"
              );
              invalidateLoyaltyCaches(selectedCustomer.id);
            } else {
              console.warn(
                "⚠️ Modal flow loyalty processing failed:",
                loyaltyResult
              );
            }
          } catch (error) {
            console.error("❌ Modal flow loyalty processing error:", error);
            loyaltyResult = {
              success: false,
              pointsAdded: 0,
              newBalance: 0,
              tierChanged: false,
              transactionId: "",
              error: error instanceof Error ? error.message : "Unknown error",
            } as any; // ✅ FIXED: Cast to any to allow error property
          }
        } else if (loyaltyResult?.success) {
          console.log(
            "✅ Modal flow loyalty points processed automatically by backend:",
            loyaltyResult
          );
        }

        // Generate order data for completion flow
        const orderDataForCompletion = {
          id: shopifyResult.shopifyOrderId || `ORDER_${Date.now()}`,
          orderNumber: shopifyResult.orderNumber || transactionId,
          totalPrice: finalOrderTotal.toString(), // ✅ FIXED: Use finalOrderTotal (includes shipping)
          createdAt: new Date().toISOString(),
          salespersonName: user?.name || "Unknown Staff",
          salespersonId: selectedAgent?.id,
          paymentMethod: paymentMethodName,
          paymentTransactionId: transactionId,
          customer: selectedCustomer
            ? {
                id: selectedCustomer.id,
                firstName: selectedCustomer.firstName,
                lastName: selectedCustomer.lastName,
                email: selectedCustomer.email,
                phone: selectedCustomer.phone,
                loyaltyData: selectedCustomer.loyaltyData, // Include loyalty data for receipt
              }
            : undefined,
          lineItems: displayCartItems.map((item) => ({
            id: item.variantId,
            title: item.title,
            quantity: item.quantity,
            price: item.price,
            sku: item.sku,
            variantId: item.variantId,
            productId: item.productId,
            // ✅ CRITICAL FIX: Include discount data for receipt generation
            discount: item.discount,
          })),
          // ✅ FIXED: Include shipping information for receipt generation
          shippingData: shippingData,
          shipping_lines:
            shippingData.fee > 0 && shippingData.data
              ? [
                  {
                    title:
                      shippingData.data.deliveryMethod || "Standard Delivery",
                    price: shippingData.fee.toString(),
                    code:
                      shippingData.data.deliveryMethod
                        ?.toLowerCase()
                        .replace(/\s+/g, "_") || "standard",
                  },
                ]
              : [],
          // ✅ FIXED: Include loyalty completion data for receipt generation
          loyaltyCompletion: loyaltyResult,
        };

        // Store order data for manual receipt printing
        setLastOrderData(orderDataForCompletion);

        return {
          success: true,
          orderData: orderDataForCompletion,
          orderNumber: shopifyResult.orderNumber || transactionId,
        };
      } else {
        throw new Error(
          shopifyResult.error || "Failed to create Shopify order"
        );
      }
    } catch (error) {
      console.error("Failed to place order in modal:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to place order. Please try again.",
      };
    }
  };

  // REMOVED: createOrderWithPayment - consolidated into handlePlaceOrderInModal

  const handlePlaceOrder = async () => {
    // Basic validation
    if (displayCartItems.length === 0) {
      setModalData({
        title: "Error",
        message: "Your cart is empty",
      });
      setShowErrorModal(true);
      return;
    }

    if (!selectedCustomer) {
      setModalData({
        title: "Error",
        message: "Please select a customer",
      });
      setShowErrorModal(true);
      return;
    }

    // Validate inventory before placing order
    const inventoryValidation = validateCartInventory(displayCartItems);
    if (!inventoryValidation.isValid) {
      setModalData({
        title: "Insufficient Inventory",
        message: `Cannot place order due to insufficient stock:\n\n${inventoryValidation.errors.join(
          "\n"
        )}`,
      });
      setShowErrorModal(true);
      return;
    }

    // For enhanced payment methods, validate payment method
    if (selectedPaymentMethod && !isPaymentMethodValid) {
      setModalData({
        title: "Payment Method Required",
        message: "Please select and configure a payment method to proceed.",
      });
      setShowErrorModal(true);
      return;
    }

    // NEW FLOW: Skip shipping modal since shipping decision is now inline
    // Proceed directly to printer check modal
    setPendingOrderAction("cash"); // Default to cash for enhanced payment flow
    setShowPreOrderPrinterCheck(true);
  };

  // REMOVED: handleShippingConfirm - no longer needed with inline shipping decision

  // REMOVED: handleSplitPaymentComplete - consolidated into handlePlaceOrderInModal

  // Helper function to calculate discounted total for an item
  const calculateDiscountedTotal = (item: any): number => {
    const lineTotal = parseFloat(item.price) * item.quantity;
    if (!item.discount || item.discount.amount <= 0) return lineTotal;

    const discountAmount =
      item.discount.type === "percentage"
        ? (lineTotal * item.discount.amount) / 100
        : Math.min(item.discount.amount, lineTotal);

    return Math.max(0, lineTotal - discountAmount);
  };

  const renderCartItem = (item: any, index: number) => {
    const isLastItem = index === cartItems.length - 1;
    const itemTotal = (parseFloat(item.price) || 0) * item.quantity;
    const discountedTotal = calculateDiscountedTotal(item);

    const handleQuantityDecrease = () => {
      const newQuantity = item.quantity - 1;
      console.log(`🔢 Attempting to decrease quantity for ${item.title}:`, {
        currentQuantity: item.quantity,
        newQuantity,
        variantId: item.variantId,
      });

      if (newQuantity <= 0) {
        console.log(`🗑️ Removing ${item.title} from cart`);
        // Remove item from cart using unified cart interface
        cart.removeItem(item.variantId);
      } else {
        console.log(`✅ Updating quantity for ${item.title} to ${newQuantity}`);
        // Update quantity using unified cart interface
        cart.updateQuantity(item.variantId, newQuantity);
      }
    };

    const handleQuantityIncrease = () => {
      const newQuantity = item.quantity + 1;
      console.log(`🔢 Attempting to increase quantity for ${item.title}:`, {
        currentQuantity: item.quantity,
        newQuantity,
        inventoryQuantity: item.inventoryQuantity,
        variantId: item.variantId,
      });

      // Check inventory limit (allow if no inventory limit is set)
      if (!item.inventoryQuantity || newQuantity <= item.inventoryQuantity) {
        console.log(`✅ Dispatching quantity update for ${item.title}`);
        cart.updateQuantity(item.variantId, newQuantity);
      } else {
        console.log(`❌ Inventory limit reached for ${item.title}`);
        setModalData({
          title: "Insufficient Inventory",
          message: `Only ${item.inventoryQuantity} units available for ${item.title}`,
        });
        setShowErrorModal(true);
      }
    };

    return (
      <View
        key={`cart-item-${item.variantId || index}`}
        style={[
          styles.cartItem,
          // Only show border if not the last item and there are multiple items
          !isLastItem && cartItems.length > 1 && styles.cartItemWithBorder,
        ]}
      >
        {/* Top row: Item title and quantity controls */}
        <View style={styles.itemTopRow}>
          <Text
            style={[styles.itemTitle, { color: textColor }]}
            numberOfLines={2}
            ellipsizeMode="tail"
          >
            {item.title}
          </Text>

          {/* Quantity controls on the right */}
          <View style={styles.quantityControls}>
            <TouchableOpacity
              style={[
                styles.quantityButton,
                {
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.border,
                },
              ]}
              onPress={handleQuantityDecrease}
            >
              <IconSymbol name="minus" size={16} color={theme.colors.text} />
            </TouchableOpacity>

            <Text style={[styles.quantityText, { color: textColor }]}>
              {item.quantity}
            </Text>

            <TouchableOpacity
              style={[
                styles.quantityButton,
                {
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.border,
                },
              ]}
              onPress={handleQuantityIncrease}
            >
              <IconSymbol name="plus" size={16} color={theme.colors.text} />
            </TouchableOpacity>

            {/* Remove button */}
            <TouchableOpacity
              onPress={() => cart.removeItem(item.variantId)}
              style={styles.removeButton}
            >
              <IconSymbol name="xmark" size={16} color={theme.colors.error} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Bottom row: Unit price and total */}
        <View style={styles.itemBottomRow}>
          <Text style={[styles.itemUnitPrice, { color: textSecondary }]}>
            @ KSh{" "}
            {(parseFloat(item.price) || 0).toLocaleString("en-KE", {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </Text>

          {/* Spacer to push total to the right */}
          <View style={{ flex: 1 }} />

          <Text style={[styles.itemTotal, { color: primaryColor }]}>
            KSh{" "}
            {discountedTotal.toLocaleString("en-KE", {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </Text>
        </View>

        {/* Discount section below the price row, aligned to the right */}
        <View style={styles.discountRow}>
          <View style={{ flex: 1 }} />
          <TouchableOpacity
            style={[
              styles.discountButton,
              {
                backgroundColor: item.discount
                  ? theme.colors.success + "20"
                  : theme.colors.surface,
                borderColor: item.discount
                  ? theme.colors.success
                  : theme.colors.border,
              },
            ]}
            onPress={() => {
              try {
                console.log("🏷️ Discount button clicked for:", item.title);
                console.log("🏷️ Current theme colors:", {
                  textColor: textColor || "fallback",
                  textSecondary: textSecondary || "fallback",
                  primaryColor: primaryColor || "fallback",
                  backgroundColor: backgroundColor || "fallback",
                });

                // Open discount modal
                setSelectedItemForDiscount(item);
                setDiscountAmount(
                  item.discount ? item.discount.amount.toString() : ""
                );
                setDiscountType(item.discount?.type || "percentage");
                setShowDiscountModal(true);
                console.log("🏷️ Modal state set to true");
              } catch (error) {
                console.error("🏷️ Error opening discount modal:", error);
                alert(
                  "Error opening discount modal: " +
                    (error instanceof Error ? error.message : String(error))
                );
              }
            }}
          >
            <Text
              style={[
                styles.discountButtonText,
                {
                  color: item.discount
                    ? theme.colors.success
                    : theme.colors.textSecondary,
                },
              ]}
            >
              {item.discount
                ? `Discount ${
                    item.discount.type === "percentage"
                      ? `-${item.discount.amount}%`
                      : `-KSh ${item.discount.amount}`
                  }`
                : "Discount"}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <ScreenWrapper title="Checkout" showBackButton>
      <ScrollView style={[styles.container, { backgroundColor }]}>
        {/* Order Summary */}
        <ModernCard style={styles.orderSummaryCard} variant="elevated">
          {/* Fixed Header */}
          <View style={styles.orderSummaryHeader}>
            <Text style={[styles.sectionTitle, { color: textColor }]}>
              Order Summary
            </Text>
            <View
              style={[styles.itemCountBadge, { backgroundColor: primaryColor }]}
            >
              <Text style={styles.itemCountText}>{displayItemCount}</Text>
            </View>
          </View>

          {/* Scrollable Cart Items */}
          <ScrollView
            style={styles.cartItemsScrollContainer}
            contentContainerStyle={styles.cartItemsScrollContent}
            showsVerticalScrollIndicator={false}
            nestedScrollEnabled={true}
            bounces={false}
          >
            <View style={styles.cartItems}>
              {displayCartItems.map((item, index) =>
                renderCartItem(item, index)
              )}
            </View>
          </ScrollView>

          {/* Fixed Footer */}
          <View style={styles.totalSection}>
            {/* Subtotal */}
            <View style={styles.totalRow}>
              <Text style={[styles.totalLabel, { color: textColor }]}>
                Subtotal
              </Text>
              <Text style={[styles.totalValue, { color: textColor }]}>
                {formatCurrency(displayCartSubtotal)}
              </Text>
            </View>

            {/* Shipping Fee (if applicable) */}
            {shippingData.fee > 0 && shippingData.data && (
              <View style={styles.totalRow}>
                <Text style={[styles.totalLabel, { color: textColor }]}>
                  Shipping ({shippingData.data.deliveryMethod})
                </Text>
                <Text style={[styles.totalValue, { color: textColor }]}>
                  {formatCurrency(shippingData.fee)}
                </Text>
              </View>
            )}

            {/* Final Total */}
            <View
              style={[
                styles.finalTotalRow,
                { borderTopColor: theme.colors.border },
              ]}
            >
              <Text
                style={[
                  styles.totalLabel,
                  { color: textColor, fontWeight: "600" },
                ]}
              >
                Total
              </Text>
              <Text style={[styles.totalAmount, { color: primaryColor }]}>
                {formatCurrency(finalOrderTotal)}
              </Text>
            </View>
          </View>

          {/* Loyalty Points Preview */}
          {selectedCustomer && displayCartTotal > 0 && (
            <View style={styles.loyaltyPreview}>
              <View style={styles.loyaltyPreviewRow}>
                <View style={styles.loyaltyPreviewLeft}>
                  <IconSymbol
                    name="star.fill"
                    size={16}
                    color={theme.colors.warning}
                  />
                  <Text
                    style={[
                      styles.loyaltyPreviewLabel,
                      { color: textSecondary },
                    ]}
                  >
                    Points to earn:
                  </Text>
                </View>
                <Text
                  style={[
                    styles.loyaltyPreviewValue,
                    { color: theme.colors.warning },
                  ]}
                >
                  +{Math.floor(displayCartTotal / 100)} points
                </Text>
              </View>
              {selectedCustomer.loyaltyData && (
                <View style={styles.loyaltyPreviewRow}>
                  <View style={styles.loyaltyPreviewLeft}>
                    <IconSymbol
                      name="creditcard.fill"
                      size={16}
                      color={theme.colors.success}
                    />
                    <Text
                      style={[
                        styles.loyaltyPreviewLabel,
                        { color: textSecondary },
                      ]}
                    >
                      Current balance:
                    </Text>
                  </View>
                  <Text
                    style={[
                      styles.loyaltyPreviewValue,
                      { color: theme.colors.success },
                    ]}
                  >
                    {selectedCustomer.loyaltyData.loyaltyPoints || 0} points
                  </Text>
                </View>
              )}
            </View>
          )}
        </ModernCard>

        {/* Inventory Validation */}
        <CartInventoryValidator
          cartItems={displayCartItems}
          showWarnings={true}
        />

        {/* Customer Information */}
        <ModernCard style={styles.section} variant="elevated">
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: textColor }]}>
              Customer
            </Text>
            <TouchableOpacity
              onPress={() => setShowCustomerModal(true)}
              style={styles.changeButton}
            >
              <Text style={[styles.changeButtonText, { color: primaryColor }]}>
                Change
              </Text>
            </TouchableOpacity>
          </View>

          {selectedCustomer ? (
            <CustomerCardWithLoyalty
              customer={selectedCustomerToCustomer(selectedCustomer)}
              showLoyalty={true}
              compact={false}
            />
          ) : (
            <TouchableOpacity
              style={styles.selectButton}
              onPress={() => setShowCustomerModal(true)}
            >
              <IconSymbol name="plus.circle" size={24} color={primaryColor} />
              <Text style={[styles.selectButtonText, { color: primaryColor }]}>
                Select Customer
              </Text>
            </TouchableOpacity>
          )}
        </ModernCard>

        {/* Sales Agent Information */}
        <ModernCard style={styles.section} variant="elevated">
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: textColor }]}>
              Sales Agent
            </Text>
            <TouchableOpacity
              onPress={() => setShowSalesAgentModal(true)}
              style={styles.changeButton}
            >
              <Text style={[styles.changeButtonText, { color: primaryColor }]}>
                {selectedAgent ? "Change" : "Select"}
              </Text>
            </TouchableOpacity>
          </View>

          {selectedAgent ? (
            <View style={styles.agentInfo}>
              <View
                style={[styles.agentAvatar, { backgroundColor: successColor }]}
              >
                <Text style={styles.agentAvatarText}>
                  {selectedAgent.name
                    .split(" ")
                    .map((n: string) => n.charAt(0))
                    .join("")
                    .slice(0, 2)}
                </Text>
              </View>
              <View style={styles.agentDetails}>
                <Text style={[styles.agentName, { color: textColor }]}>
                  {selectedAgent.name}
                </Text>
                {/* TODO: Commission display temporarily hidden - will be replaced with proper commission system */}
                {/* <Text style={[styles.agentCommission, { color: successColor }]}>
                  {selectedAgent.commissionRate}% commission
                </Text> */}
                {selectedAgent.email && (
                  <Text style={[styles.agentContact, { color: textSecondary }]}>
                    {selectedAgent.email}
                  </Text>
                )}
              </View>
            </View>
          ) : (
            <View style={styles.noAgentInfo}>
              <Text style={[styles.noAgentText, { color: textSecondary }]}>
                No sales agent selected
              </Text>
              {/* TODO: Commission tracking text temporarily hidden - will be replaced with proper commission system */}
              {/* <Text style={[styles.noAgentSubtext, { color: textSecondary }]}>
                Commission will not be tracked for this sale
              </Text> */}
            </View>
          )}
        </ModernCard>

        {/* Shipping Decision Section - Mandatory before payment */}
        {selectedCustomer && displayCartItems.length > 0 && (
          <ShippingDecisionSection
            cartTotal={displayCartTotal}
            onShippingUpdate={handleShippingUpdate}
            disabled={!selectedCustomer}
            selectedCustomer={selectedCustomer}
          />
        )}

        {/* Enhanced Payment Flow Manager - Inline */}
        {isPaymentEnabled && displayCartItems.length > 0 ? (
          <ModernCard style={styles.section} variant="elevated">
            <PaymentFlowManager
              totalAmount={finalOrderTotal}
              currency="KES"
              onPaymentComplete={handleEnhancedPaymentComplete}
              orderData={{
                line_items: displayCartItems.map((item) => ({
                  variant_id: item.variantId,
                  quantity: item.quantity,
                  price: item.price.toString(),
                  title: item.title,
                  sku: item.sku,
                })),
                customer: selectedCustomer
                  ? {
                      id: selectedCustomer.id,
                      first_name: selectedCustomer.firstName,
                      last_name: selectedCustomer.lastName,
                      email: selectedCustomer.email,
                      phone: selectedCustomer.phone,
                    }
                  : undefined,
              }}
              customerId={selectedCustomer?.id}
              customerName={
                selectedCustomer?.displayName || selectedCustomer?.firstName
              }
              customerPhone={selectedCustomer?.phone}
              customerEmail={selectedCustomer?.email}
              staffId={user?.id}
              terminalId="TERMINAL_001" // This should come from terminal configuration
              locationId={currentLocation?.id}
              allowSplitPayment={true}
              disabled={!isShippingDecided}
              onPaymentMethodChange={handlePaymentMethodChange}
            />
          </ModernCard>
        ) : (
          <ModernCard style={styles.section} variant="elevated">
            <Text style={[styles.sectionTitle, { color: textColor }]}>
              Payment Processing
            </Text>

            <Text style={[styles.sectionDescription, { color: textSecondary }]}>
              Choose from multiple payment methods including Cash, M-Pesa, ABSA
              Till, Card, and Credit. Split payments across multiple methods are
              supported.
            </Text>

            {!selectedCustomer && (
              <View style={styles.paymentRequirement}>
                <IconSymbol
                  name="info.circle"
                  size={16}
                  color={theme.colors.warning}
                />
                <Text
                  style={[
                    styles.paymentRequirementText,
                    { color: theme.colors.warning },
                  ]}
                >
                  Please select a customer to proceed with payment
                </Text>
              </View>
            )}

            {displayCartItems.length === 0 && (
              <View style={styles.paymentRequirement}>
                <IconSymbol
                  name="info.circle"
                  size={16}
                  color={theme.colors.warning}
                />
                <Text
                  style={[
                    styles.paymentRequirementText,
                    { color: theme.colors.warning },
                  ]}
                >
                  Add items to cart to proceed with payment
                </Text>
              </View>
            )}

            {selectedCustomer &&
              displayCartItems.length > 0 &&
              !isShippingDecided && (
                <View style={styles.paymentRequirement}>
                  <IconSymbol
                    name="info.circle"
                    size={16}
                    color={theme.colors.warning}
                  />
                  <Text
                    style={[
                      styles.paymentRequirementText,
                      { color: theme.colors.warning },
                    ]}
                  >
                    Please complete shipping decision above to proceed with
                    payment
                  </Text>
                </View>
              )}
          </ModernCard>
        )}

        {/* Order Summary */}
        <View style={styles.checkoutSummary}>
          <Text style={[styles.summaryNote, { color: textSecondary }]}>
            Review your order details above, then proceed with payment
            processing. All payment methods and split payments are supported.
          </Text>
        </View>

        {/* Place Order Button */}
        {selectedCustomer && displayCartItems.length > 0 && (
          <View style={styles.checkoutActions}>
            <ModernButton
              title={isProcessingOrder ? "Processing..." : "Place Order"}
              onPress={handlePlaceOrder}
              loading={isProcessingOrder}
              disabled={
                isProcessingOrder ||
                displayCartItems.length === 0 ||
                !selectedCustomer ||
                !isShippingDecided ||
                !isPaymentMethodValid
              }
              size="lg"
              style={styles.placeOrderButton}
            />

            <Text style={[styles.disclaimer, { color: textSecondary }]}>
              By placing this order, you confirm that all information is correct
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Success Modal */}
      <SuccessModal
        visible={showSuccessModal}
        onClose={handleSuccessModalDismiss}
        title={modalData.title}
        message={`${modalData.message}\n\nTotal: KSh ${
          modalData.orderTotal?.toFixed(2) || "0.00"
        }`}
        actions={[
          {
            title: "Print Receipt",
            onPress: handlePrintReceiptFromSuccess,
            variant: "outline",
            icon: "printer",
            dismissModal: false, // Don't dismiss - let user see print result
          },
          {
            title: "View Orders",
            onPress: handleViewOrdersFromSuccess,
            variant: "outline",
            icon: "list.bullet",
            dismissModal: true, // Dismiss and navigate
          },
          {
            title: "New Sale",
            onPress: handleNewSaleFromSuccess,
            variant: "primary",
            icon: "plus.circle",
            dismissModal: true, // Dismiss and navigate
          },
          {
            title: "View Receipt",
            onPress: handleViewReceiptFromSuccess,
            variant: "outline",
            icon: "doc.text",
            dismissModal: true, // Dismiss and navigate
          },
        ]}
      />

      {/* Error Modal */}
      <ErrorModal
        visible={showErrorModal}
        onClose={() => setShowErrorModal(false)}
        title={modalData.title}
        message={modalData.message}
        showRetryButton={modalData.message.includes("Failed to place order")}
        onRetry={() => handlePlaceOrder()}
      />

      {/* Shipping Options Modal - DISABLED: Now using inline shipping decision */}
      {false && selectedCustomer && (
        <ShippingOptionsModal
          visible={showShippingModal}
          onClose={() => setShowShippingModal(false)}
          onConfirm={handleShippingConfirm}
          customer={selectedCustomer}
          orderTotal={displayCartTotal}
          cartWeight={displayCartItems.reduce(
            (total, item) => total + item.quantity * 0.5,
            0
          )} // Estimate 0.5kg per item
        />
      )}

      {/* Confirmation Modal for Printer Setup */}
      <ConfirmationModal
        visible={showConfirmationModal}
        onClose={() => setShowConfirmationModal(false)}
        title={modalData.title}
        message={modalData.message}
        icon="printer"
        actions={[
          {
            title: "Cancel",
            onPress: () => setShowConfirmationModal(false),
            variant: "outline",
          },
          {
            title: "Setup Printer",
            onPress: () => router.push("/thermal-printer-setup"),
            variant: "primary",
            icon: "gear",
          },
        ]}
      />

      {/* Order Completion Modal */}
      {completionOrderData && (
        <OrderCompletionModal
          visible={showOrderCompletionModal}
          onClose={() => setShowOrderCompletionModal(false)}
          onComplete={handleOrderCompletion}
          orderData={completionOrderData.orderData}
          orderNumber={completionOrderData.orderNumber}
          orderTotal={completionOrderData.orderTotal}
          paymentMethod={completionOrderData.paymentMethod}
          transactionId={completionOrderData.transactionId}
          printingAlreadyAttempted={
            completionOrderData.printingAlreadyAttempted
          }
          printingWasSuccessful={completionOrderData.printingWasSuccessful}
        />
      )}

      {/* Pre-Order Printer Check Modal */}
      <PreOrderPrinterCheckModal
        visible={showPreOrderPrinterCheck}
        onClose={() => setShowPreOrderPrinterCheck(false)}
        orderTotal={finalOrderTotal}
        paymentMethod={
          selectedPaymentMethod?.method?.type === "split"
            ? "Split Payment"
            : selectedPaymentMethod?.method?.name || "Cash"
        }
        onPlaceOrder={handlePlaceOrderInModal}
        onOrderComplete={handleOrderCompletion}
      />

      {/* Customer Selection Modal */}
      <Modal
        visible={showCustomerModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowCustomerModal(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor }]}>
          <View
            style={[styles.modalHeader, { borderBottomColor: borderColor }]}
          >
            <Text style={[styles.modalTitle, { color: textColor }]}>
              Select Customer
            </Text>
            <TouchableOpacity
              onPress={() => setShowCustomerModal(false)}
              style={styles.modalCloseButton}
            >
              <IconSymbol name="xmark" size={24} color={textColor} />
            </TouchableOpacity>
          </View>

          <View style={styles.modalSearchContainer}>
            <TextInput
              style={[
                styles.modalSearchInput,
                {
                  borderColor: borderColor,
                  color: textColor,
                  backgroundColor: theme.colors.surface,
                },
              ]}
              value={customerSearchQuery}
              onChangeText={(text) => {
                setCustomerSearchQuery(text);
                loadCustomers(text);
              }}
              placeholder="Search customers..."
              placeholderTextColor={textSecondary}
              autoFocus
            />
          </View>

          {isLoadingCustomers ? (
            <View style={styles.modalLoadingContainer}>
              <ActivityIndicator size="large" color={primaryColor} />
              <Text style={[styles.modalLoadingText, { color: textSecondary }]}>
                Loading customers...
              </Text>
            </View>
          ) : (
            <FlatList
              data={customers}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <CustomerCardWithLoyalty
                  customer={item}
                  onPress={handleCustomerSelect}
                  showLoyalty={true}
                  compact={true}
                  style={[
                    styles.modalListItem,
                    { borderBottomColor: borderColor },
                  ]}
                />
              )}
              contentContainerStyle={styles.modalListContainer}
              showsVerticalScrollIndicator={false}
            />
          )}

          {/* Floating Action Button for Customer Creation */}
          <FloatingActionButton
            iconName="person.badge.plus"
            onPress={() => setShowCustomerCreationModal(true)}
            accessibilityLabel="Create new customer"
            bottom={theme.spacing.xl}
            right={theme.spacing.xl}
          />
        </View>
      </Modal>

      {/* Sales Agent Selection Modal */}
      <Modal
        visible={showSalesAgentModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowSalesAgentModal(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor }]}>
          <View
            style={[styles.modalHeader, { borderBottomColor: borderColor }]}
          >
            <Text style={[styles.modalTitle, { color: textColor }]}>
              Select Sales Agent
            </Text>
            <TouchableOpacity
              onPress={() => setShowSalesAgentModal(false)}
              style={styles.modalCloseButton}
            >
              <IconSymbol name="xmark" size={24} color={textColor} />
            </TouchableOpacity>
          </View>

          <View style={styles.modalSearchContainer}>
            <TextInput
              style={[
                styles.modalSearchInput,
                {
                  borderColor: borderColor,
                  color: textColor,
                  backgroundColor: theme.colors.surface,
                },
              ]}
              value={agentSearchQuery}
              onChangeText={(text) => {
                setAgentSearchQuery(text);
                loadSalesAgents(text);
              }}
              placeholder="Search sales agents..."
              placeholderTextColor={textSecondary}
              autoFocus
            />
          </View>

          {isLoadingSalesAgents ? (
            <View style={styles.modalLoadingContainer}>
              <ActivityIndicator size="large" color={primaryColor} />
              <Text style={[styles.modalLoadingText, { color: textSecondary }]}>
                Loading sales agents...
              </Text>
            </View>
          ) : (
            <FlatList
              data={salesAgents}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.modalListItem,
                    { borderBottomColor: borderColor },
                  ]}
                  onPress={() => handleSalesAgentSelect(item)}
                >
                  <View
                    style={[
                      styles.modalItemAvatar,
                      { backgroundColor: successColor },
                    ]}
                  >
                    <Text style={styles.modalItemAvatarText}>
                      {item.name
                        .split(" ")
                        .map((n: string) => n.charAt(0))
                        .join("")
                        .slice(0, 2)}
                    </Text>
                  </View>
                  <View style={styles.modalItemInfo}>
                    <Text style={[styles.modalItemName, { color: textColor }]}>
                      {item.name}
                    </Text>
                    {/* TODO: Commission display temporarily hidden - will be replaced with proper commission system */}
                    {/* <Text
                      style={[
                        styles.modalItemCommission,
                        { color: successColor },
                      ]}
                    >
                      {item.commissionRate}% commission
                    </Text> */}
                    {item.email && (
                      <Text
                        style={[
                          styles.modalItemContact,
                          { color: textSecondary },
                        ]}
                      >
                        {item.email}
                      </Text>
                    )}
                  </View>
                  <IconSymbol
                    name="chevron.right"
                    size={16}
                    color={textSecondary}
                  />
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.modalListContainer}
              showsVerticalScrollIndicator={false}
            />
          )}
        </View>
      </Modal>

      {/* Customer Ticket Resume Modal */}
      {selectedCustomer && (
        <CustomerTicketResumeModal
          visible={showCustomerTicketResumeModal}
          onClose={() => setShowCustomerTicketResumeModal(false)}
          customer={selectedCustomerToCustomer(selectedCustomer)}
          existingTickets={customerTickets}
          currentCartItems={cartItems}
          onAction={handleTicketResumeAction}
          isLoading={isLoadingCustomerTickets}
        />
      )}

      {/* Custom Overlay Discount Modal */}
      {showDiscountModal && (
        <View
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            justifyContent: "center",
            alignItems: "center",
            padding: 20,
            zIndex: 1000,
          }}
        >
          <View
            style={{
              width: "100%",
              maxWidth: 400,
              backgroundColor: backgroundColor || "#FFFFFF",
              borderRadius: 12,
              padding: 20,
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.25,
              shadowRadius: 8,
              elevation: 5,
            }}
          >
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: 20,
              }}
            >
              <Text
                style={{
                  fontSize: 18,
                  fontWeight: "600",
                  color: textColor || "#000000",
                }}
              >
                Apply Discount
              </Text>
              <TouchableOpacity onPress={() => setShowDiscountModal(false)}>
                <Text
                  style={{
                    fontSize: 18,
                    color: textSecondary || "#666666",
                  }}
                >
                  ✕
                </Text>
              </TouchableOpacity>
            </View>

            {selectedItemForDiscount && (
              <View
                style={{
                  marginBottom: 20,
                  padding: 12,
                  backgroundColor: theme?.colors?.surface || "#F5F5F5",
                  borderRadius: 8,
                }}
              >
                <Text
                  style={{
                    fontSize: 16,
                    fontWeight: "600",
                    marginBottom: 4,
                    color: textColor || "#000000",
                  }}
                >
                  {selectedItemForDiscount.title}
                </Text>
                <Text
                  style={{
                    fontSize: 14,
                    color: textSecondary || "#666666",
                  }}
                >
                  Line Total: KSh{" "}
                  {(
                    parseFloat(selectedItemForDiscount.price) *
                    selectedItemForDiscount.quantity
                  ).toFixed(2)}
                </Text>
              </View>
            )}

            {/* Discount Type Selection */}
            <View style={{ marginBottom: 16 }}>
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: "600",
                  marginBottom: 8,
                  color: textColor || "#000000",
                }}
              >
                Discount Type
              </Text>
              <View style={{ flexDirection: "row", gap: 8 }}>
                <TouchableOpacity
                  style={{
                    flex: 1,
                    paddingVertical: 12,
                    paddingHorizontal: 16,
                    borderRadius: 8,
                    borderWidth: 1,
                    borderColor: theme?.colors?.border || "#E0E0E0",
                    backgroundColor:
                      discountType === "percentage"
                        ? primaryColor || "#007AFF"
                        : theme?.colors?.surface || "#F5F5F5",
                    alignItems: "center",
                  }}
                  onPress={() => setDiscountType("percentage")}
                >
                  <Text
                    style={{
                      fontSize: 14,
                      fontWeight: "500",
                      color:
                        discountType === "percentage"
                          ? "#FFFFFF"
                          : textColor || "#000000",
                    }}
                  >
                    Percentage (%)
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={{
                    flex: 1,
                    paddingVertical: 12,
                    paddingHorizontal: 16,
                    borderRadius: 8,
                    borderWidth: 1,
                    borderColor: theme?.colors?.border || "#E0E0E0",
                    backgroundColor:
                      discountType === "fixed_amount"
                        ? primaryColor || "#007AFF"
                        : theme?.colors?.surface || "#F5F5F5",
                    alignItems: "center",
                  }}
                  onPress={() => setDiscountType("fixed_amount")}
                >
                  <Text
                    style={{
                      fontSize: 14,
                      fontWeight: "500",
                      color:
                        discountType === "fixed_amount"
                          ? "#FFFFFF"
                          : textColor || "#000000",
                    }}
                  >
                    Fixed Amount
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Discount Amount Input */}
            <View style={{ marginBottom: 16 }}>
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: "600",
                  marginBottom: 8,
                  color: textColor || "#000000",
                }}
              >
                {discountType === "percentage" ? "Percentage" : "Amount (KSh)"}
              </Text>
              <TextInput
                style={{
                  borderWidth: 1,
                  borderColor: theme?.colors?.border || "#E0E0E0",
                  borderRadius: 8,
                  paddingHorizontal: 12,
                  paddingVertical: 10,
                  fontSize: 16,
                  color: textColor || "#000000",
                  backgroundColor: theme?.colors?.surface || "#F5F5F5",
                }}
                value={discountAmount}
                onChangeText={setDiscountAmount}
                placeholder={discountType === "percentage" ? "10" : "5.00"}
                placeholderTextColor={textSecondary || "#666666"}
                keyboardType="numeric"
                maxLength={discountType === "percentage" ? 3 : 10}
              />
            </View>

            {/* Action Buttons */}
            <View
              style={{
                flexDirection: "row",
                gap: 12,
                marginTop: 20,
              }}
            >
              {selectedItemForDiscount?.discount && (
                <TouchableOpacity
                  style={{
                    flex: 1,
                    padding: 12,
                    backgroundColor: "transparent",
                    borderWidth: 1,
                    borderColor: theme?.colors?.error || "#FF0000",
                    borderRadius: 8,
                    alignItems: "center",
                  }}
                  onPress={handleRemoveDiscount}
                >
                  <Text
                    style={{
                      color: theme?.colors?.error || "#FF0000",
                      fontWeight: "500",
                    }}
                  >
                    Remove Discount
                  </Text>
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={{
                  flex: 1,
                  padding: 12,
                  backgroundColor: primaryColor || "#007AFF",
                  borderRadius: 8,
                  alignItems: "center",
                }}
                onPress={handleApplyDiscount}
              >
                <Text style={{ color: "#FFFFFF", fontWeight: "500" }}>
                  Apply Discount
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {/* Customer Creation Modal */}
      <Modal
        visible={showCustomerCreationModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowCustomerCreationModal(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor }]}>
          <View
            style={[styles.modalHeader, { borderBottomColor: borderColor }]}
          >
            <Text style={[styles.modalTitle, { color: textColor }]}>
              Create New Customer
            </Text>
            <TouchableOpacity
              onPress={() => setShowCustomerCreationModal(false)}
              style={styles.modalCloseButton}
            >
              <IconSymbol name="xmark" size={24} color={textColor} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.customerFormContainer}>
            <View style={styles.customerFormField}>
              <Text style={[styles.customerFormLabel, { color: textColor }]}>
                First Name *
              </Text>
              <TextInput
                style={[
                  styles.customerFormInput,
                  {
                    borderColor: borderColor,
                    color: textColor,
                    backgroundColor: theme.colors.surface,
                  },
                ]}
                value={customerFormData.firstName}
                onChangeText={(text) =>
                  setCustomerFormData({ ...customerFormData, firstName: text })
                }
                placeholder="Enter first name"
                placeholderTextColor={textSecondary}
                autoFocus
              />
            </View>

            <View style={styles.customerFormField}>
              <Text style={[styles.customerFormLabel, { color: textColor }]}>
                Last Name *
              </Text>
              <TextInput
                style={[
                  styles.customerFormInput,
                  {
                    borderColor: borderColor,
                    color: textColor,
                    backgroundColor: theme.colors.surface,
                  },
                ]}
                value={customerFormData.lastName}
                onChangeText={(text) =>
                  setCustomerFormData({ ...customerFormData, lastName: text })
                }
                placeholder="Enter last name"
                placeholderTextColor={textSecondary}
              />
            </View>

            <View style={styles.customerFormField}>
              <Text style={[styles.customerFormLabel, { color: textColor }]}>
                Email
              </Text>
              <TextInput
                style={[
                  styles.customerFormInput,
                  {
                    borderColor: borderColor,
                    color: textColor,
                    backgroundColor: theme.colors.surface,
                  },
                ]}
                value={customerFormData.email}
                onChangeText={(text) =>
                  setCustomerFormData({ ...customerFormData, email: text })
                }
                placeholder="Enter email address"
                placeholderTextColor={textSecondary}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            <View style={styles.customerFormField}>
              <Text style={[styles.customerFormLabel, { color: textColor }]}>
                Phone
              </Text>
              <TextInput
                style={[
                  styles.customerFormInput,
                  {
                    borderColor: borderColor,
                    color: textColor,
                    backgroundColor: theme.colors.surface,
                  },
                ]}
                value={customerFormData.phone}
                onChangeText={(text) =>
                  setCustomerFormData({ ...customerFormData, phone: text })
                }
                placeholder="Enter phone number"
                placeholderTextColor={textSecondary}
                keyboardType="phone-pad"
              />
            </View>

            <View style={styles.customerFormActions}>
              <TouchableOpacity
                style={[
                  styles.customerFormButton,
                  styles.customerFormCancelButton,
                  { borderColor: borderColor },
                ]}
                onPress={() => setShowCustomerCreationModal(false)}
              >
                <Text
                  style={[styles.customerFormButtonText, { color: textColor }]}
                >
                  Cancel
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.customerFormButton,
                  styles.customerFormCreateButton,
                  { backgroundColor: primaryColor },
                ]}
                onPress={handleCreateCustomer}
                disabled={isCreatingCustomer}
              >
                {isCreatingCustomer ? (
                  <ActivityIndicator size="small" color="#FFFFFF" />
                ) : (
                  <Text style={styles.customerFormCreateButtonText}>
                    Create
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </ScreenWrapper>
  );
};

// Create styles function that uses theme
const createStyles = (theme: any, responsiveLayout?: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    section: {
      margin: theme.spacing.md,
    },
    orderSummaryCard: {
      margin: theme.spacing.md,
      marginBottom: theme.spacing.lg,
      // Set maximum height constraint (60% of viewport or 600px max)
      // maxHeight: Math.min(Dimensions.get("window").height * 0.6, 600),
      // Ensure proper layout structure
      flexShrink: 0,
    },
    orderSummaryHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingBottom: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      // Fixed header - prevent scrolling
      flexShrink: 0,
    },
    cartItemsScrollContainer: {
      // Scrollable middle section
      flex: 1,
      maxHeight: responsiveLayout?.isDesktop ? 400 : 300, // Responsive max height
      minHeight: 150, // Ensure minimum visibility
    },
    cartItemsScrollContent: {
      // Content container for ScrollView
      flexGrow: 1,
      paddingVertical: theme.spacing.sm,
      // minHeight: 100, // Ensure content has minimum height
    },
    itemCountBadge: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.round,
      minWidth: 28,
      height: 28,
      alignItems: "center",
      justifyContent: "center",
    },
    itemCountText: {
      ...theme.typography.caption,
      color: "#FFFFFF",
      fontWeight: "600",
      fontSize: 12,
    },
    sectionHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    sectionTitle: {
      ...theme.typography.bodyMedium,
      fontSize: theme.typography.bodyLarge.fontSize,
      marginBottom: theme.spacing.lg,
    },
    sectionSubtitle: {
      ...theme.typography.caption,
      marginBottom: theme.spacing.md,
    },
    changeButton: {
      padding: theme.spacing.sm,
    },
    changeButtonText: {
      ...theme.typography.bodyMedium,
    },
    cartItems: {
      // Remove margin since we're in a ScrollView now
      paddingHorizontal: 0,
      // Prevent layout shifts during state changes
      flexShrink: 0,
    },
    cartItem: {
      paddingVertical: theme.spacing.md,
      paddingHorizontal: 0, // No horizontal padding to use full width
      // Responsive minimum height based on screen size
      minHeight: responsiveLayout?.isDesktop ? 80 : 70,
      flexShrink: 0,
      // Full width layout
      width: "100%",
      alignSelf: "stretch",
    },
    cartItemWithBorder: {
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    itemTitle: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
      flex: 1, // Take maximum available space
      paddingRight: theme.spacing.md, // Space from controls
      textAlign: "left", // Ensure left alignment
    },
    itemTopRow: {
      flexDirection: "row",
      alignItems: "flex-start",
      justifyContent: "space-between",
      marginBottom: theme.spacing.sm,
      width: "100%", // Full width
      gap: theme.spacing.md, // Larger gap for better separation
    },
    itemBottomRow: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      width: "100%", // Full width
      // Prevent layout shifts during quantity changes
      minHeight: 32,
      flexShrink: 0,
      gap: theme.spacing.sm, // Add gap for better spacing with discount button
    },
    quantityControls: {
      flexDirection: "row",
      alignItems: "center",
      gap: theme.spacing.xs,
      // Fixed width to prevent layout shifts, positioned to far right
      minWidth: 140, // Accommodate all controls
      flexShrink: 0,
      justifyContent: "flex-end", // Align controls to the right
    },
    removeButton: {
      marginLeft: theme.spacing.sm,
      padding: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
    },
    separator: {
      ...theme.typography.bodyMedium,
      flex: 1,
      textAlign: "center",
      fontSize: 18,
      fontWeight: "300",
    },
    quantityButton: {
      width: 32,
      height: 32,
      borderRadius: theme.borderRadius.sm,
      borderWidth: 1,
      alignItems: "center",
      justifyContent: "center",
    },
    quantityText: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
      minWidth: 24,
      textAlign: "center",
    },
    itemUnitPrice: {
      ...theme.typography.caption,
      flex: 0,
      minWidth: 100,
    },
    itemQuantityPrice: {
      ...theme.typography.caption,
      flex: 0,
      minWidth: 120,
    },

    itemTotal: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
      textAlign: "right",
      minWidth: 80,
    },
    discountRow: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: theme.spacing.xs,
      paddingRight: theme.spacing.sm,
    },
    discountButton: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.small,
      borderWidth: 1,
    },
    discountButtonText: {
      ...theme.typography.caption,
      fontSize: 11,
      fontWeight: "500",
    },
    // Discount Modal Styles
    modalOverlay: {
      flex: 1,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      justifyContent: "center",
      alignItems: "center",
      padding: 20,
    },
    discountModalContent: {
      width: "100%",
      maxWidth: 400,
      borderRadius: theme.borderRadius.large,
      padding: 20,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 8,
      elevation: 5,
    },
    discountModalHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 20,
    },
    discountModalTitle: {
      fontSize: 18,
      fontWeight: "600",
    },
    discountItemInfo: {
      marginBottom: 20,
      padding: 12,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.medium,
    },
    discountItemName: {
      fontSize: 16,
      fontWeight: "600",
      marginBottom: 4,
    },
    discountItemPrice: {
      fontSize: 14,
    },
    discountTypeSection: {
      marginBottom: 20,
    },
    discountSectionLabel: {
      fontSize: 14,
      fontWeight: "600",
      marginBottom: 8,
    },
    discountTypeButtons: {
      flexDirection: "row",
      gap: 8,
    },
    discountTypeButton: {
      flex: 1,
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: theme.borderRadius.medium,
      borderWidth: 1,
      alignItems: "center",
    },
    discountTypeButtonText: {
      fontSize: 14,
      fontWeight: "500",
    },
    discountInputSection: {
      marginBottom: 16,
    },
    discountAmountInput: {
      borderWidth: 1,
      borderRadius: theme.borderRadius.medium,
      paddingHorizontal: 12,
      paddingVertical: 10,
      fontSize: 16,
    },
    discountActionButtons: {
      flexDirection: "row",
      gap: 12,
      marginTop: 20,
    },
    discountActionButton: {
      flex: 1,
    },
    totalRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingTop: theme.spacing.md,
      borderTopWidth: 1,
      // Fixed footer - prevent scrolling
      flexShrink: 0,
      backgroundColor: theme.colors.surface, // Ensure background consistency
    },
    totalLabel: {
      ...theme.typography.bodyMedium,
      fontSize: theme.typography.bodyLarge.fontSize,
    },
    totalAmount: {
      ...theme.typography.bodyMedium,
      fontSize: theme.typography.h2.fontSize,
      fontWeight: "600",
    },
    loyaltyPreview: {
      marginTop: theme.spacing.md,
      paddingTop: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      gap: theme.spacing.sm,
    },
    loyaltyPreviewRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    loyaltyPreviewLeft: {
      flexDirection: "row",
      alignItems: "center",
      gap: theme.spacing.sm,
    },
    loyaltyPreviewLabel: {
      ...theme.typography.caption,
      fontSize: 14,
    },
    loyaltyPreviewValue: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
    },
    customerInfo: {
      flexDirection: "row",
      alignItems: "center",
    },
    customerAvatar: {
      width: 50,
      height: 50,
      borderRadius: 25,
      alignItems: "center",
      justifyContent: "center",
      marginRight: theme.spacing.md,
    },
    customerAvatarText: {
      ...theme.typography.bodyMedium,
      color: "#FFFFFF",
    },
    customerDetails: {
      flex: 1,
    },
    customerName: {
      ...theme.typography.bodyMedium,
      marginBottom: 4,
    },
    customerContact: {
      ...theme.typography.caption,
      marginBottom: 2,
    },
    selectButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      padding: theme.spacing.lg,
      borderWidth: 2,
      borderColor: theme.colors.border,
      borderStyle: "dashed",
      borderRadius: theme.borderRadius.sm,
      gap: theme.spacing.sm,
    },
    selectButtonText: {
      ...theme.typography.bodyMedium,
    },
    agentInfo: {
      flexDirection: "row",
      alignItems: "center",
    },
    agentAvatar: {
      width: 50,
      height: 50,
      borderRadius: 25,
      alignItems: "center",
      justifyContent: "center",
      marginRight: theme.spacing.md,
    },
    agentAvatarText: {
      ...theme.typography.bodyMedium,
      color: "#FFFFFF",
    },
    agentDetails: {
      flex: 1,
    },
    agentName: {
      ...theme.typography.bodyMedium,
      marginBottom: 4,
    },
    agentCommission: {
      ...theme.typography.caption,
      marginBottom: 2,
    },
    agentContact: {
      ...theme.typography.caption,
    },
    noAgentInfo: {
      padding: theme.spacing.lg,
      alignItems: "center",
    },
    noAgentText: {
      ...theme.typography.body,
      marginBottom: 4,
    },
    noAgentSubtext: {
      ...theme.typography.caption,
      textAlign: "center",
    },
    paymentMethods: {
      flexDirection: "row",
      gap: theme.spacing.md,
    },
    paymentMethod: {
      flex: 1,
      alignItems: "center",
      padding: theme.spacing.md,
      borderWidth: 2,
      borderRadius: theme.borderRadius.sm,
      gap: theme.spacing.sm,
    },
    paymentMethodText: {
      ...theme.typography.caption,
    },
    checkoutActions: {
      padding: theme.spacing.md,
      paddingBottom: theme.spacing.xl,
    },
    placeOrderButton: {
      marginBottom: theme.spacing.md,
    },
    disclaimer: {
      ...theme.typography.small,
      textAlign: "center",
      lineHeight: 18,
    },
    transactionCodeSection: {
      marginTop: theme.spacing.md,
      paddingTop: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    inputLabel: {
      ...theme.typography.bodyMedium,
      marginBottom: theme.spacing.sm,
      fontWeight: "500",
    },
    transactionCodeInput: {
      borderWidth: 1,
      borderRadius: theme.borderRadius.sm,
      padding: theme.spacing.md,
      fontSize: 16,
      marginBottom: theme.spacing.sm,
    },
    inputHint: {
      ...theme.typography.caption,
      fontStyle: "italic",
    },
    splitPaymentButton: {
      flexDirection: "row",
      alignItems: "center",
      padding: theme.spacing.md,
      marginTop: theme.spacing.md,
      borderWidth: 2,
      borderRadius: theme.borderRadius.sm,
      borderStyle: "dashed",
    },
    splitPaymentContent: {
      flex: 1,
      marginLeft: theme.spacing.md,
    },
    splitPaymentTitle: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
      marginBottom: 2,
    },
    splitPaymentSubtitle: {
      ...theme.typography.caption,
    },
    // Enhanced Payment Styles
    sectionDescription: {
      ...theme.typography.caption,
      marginBottom: theme.spacing.lg,
      lineHeight: 18,
    },
    enhancedPaymentButton: {
      flexDirection: "row",
      alignItems: "center",
      padding: theme.spacing.lg,
      borderWidth: 2,
      borderRadius: theme.borderRadius.md,
      marginBottom: theme.spacing.md,
    },
    enhancedPaymentIcon: {
      marginRight: theme.spacing.md,
    },
    enhancedPaymentContent: {
      flex: 1,
    },
    enhancedPaymentTitle: {
      ...theme.typography.bodyLarge,
      fontWeight: "600",
      marginBottom: 4,
    },
    enhancedPaymentSubtitle: {
      ...theme.typography.caption,
      marginBottom: 6,
    },
    enhancedPaymentAmount: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
    },
    paymentRequirement: {
      flexDirection: "row",
      alignItems: "center",
      padding: theme.spacing.md,
      marginTop: theme.spacing.md,
      backgroundColor: `${theme.colors.warning}10`,
      borderRadius: theme.borderRadius.sm,
      gap: theme.spacing.sm,
    },
    paymentRequirementText: {
      ...theme.typography.caption,
      flex: 1,
    },
    checkoutSummary: {
      padding: theme.spacing.md,
      marginBottom: theme.spacing.xl,
    },
    summaryNote: {
      ...theme.typography.caption,
      textAlign: "center",
      lineHeight: 18,
      fontStyle: "italic",
    },
    // Modal styles
    modalContainer: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    modalHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      padding: theme.spacing.lg,
      borderBottomWidth: 1,
    },
    modalTitle: {
      ...theme.typography.h3,
      fontWeight: "600",
    },
    modalCloseButton: {
      padding: theme.spacing.sm,
    },
    modalSearchContainer: {
      padding: theme.spacing.lg,
      paddingBottom: theme.spacing.md,
    },
    modalSearchInput: {
      ...theme.typography.body,
      padding: theme.spacing.md,
      borderWidth: 1,
      borderRadius: theme.borderRadius.sm,
    },
    modalLoadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: theme.spacing.xl,
    },
    modalLoadingText: {
      ...theme.typography.body,
      marginTop: theme.spacing.md,
    },
    modalListContainer: {
      paddingHorizontal: theme.spacing.lg,
    },
    modalListItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: theme.spacing.lg,
      borderBottomWidth: 1,
    },
    modalItemAvatar: {
      width: 50,
      height: 50,
      borderRadius: 25,
      alignItems: "center",
      justifyContent: "center",
      marginRight: theme.spacing.md,
    },
    modalItemAvatarText: {
      ...theme.typography.bodyMedium,
      color: "#FFFFFF",
      fontWeight: "600",
    },
    modalItemInfo: {
      flex: 1,
    },
    modalItemName: {
      ...theme.typography.bodyMedium,
      marginBottom: 4,
      fontWeight: "500",
    },
    modalItemContact: {
      ...theme.typography.caption,
      marginBottom: 2,
    },
    modalItemCommission: {
      ...theme.typography.caption,
      marginBottom: 2,
      fontWeight: "600",
    },
    // Customer creation form styles
    customerFormContainer: {
      flex: 1,
      padding: theme.spacing.md,
    },
    customerFormField: {
      marginBottom: theme.spacing.lg,
    },
    customerFormLabel: {
      ...theme.typography.bodyMedium,
      fontWeight: "600",
      marginBottom: theme.spacing.sm,
    },
    customerFormInput: {
      borderWidth: 1,
      borderRadius: theme.borderRadius.sm,
      padding: theme.spacing.md,
      fontSize: 16,
    },
    customerFormActions: {
      flexDirection: "row",
      gap: theme.spacing.md,
      marginTop: theme.spacing.xl,
      paddingBottom: theme.spacing.xl,
    },
    customerFormButton: {
      flex: 1,
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.sm,
      alignItems: "center",
      justifyContent: "center",
      minHeight: 48,
    },
    customerFormCancelButton: {
      borderWidth: 1,
    },
    customerFormCreateButton: {
      // backgroundColor set dynamically
    },
    customerFormButtonText: {
      ...theme.typography.body,
      fontWeight: "500",
    },
    customerFormCreateButtonText: {
      ...theme.typography.body,
      fontWeight: "600",
      color: "#FFFFFF",
    },
    // Shipping total styles
    totalSection: {
      paddingTop: theme.spacing.md,
      borderTopWidth: 1,
    },
    totalValue: {
      ...theme.typography.bodyMedium,
      fontWeight: "500",
    },
    finalTotalRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingTop: theme.spacing.md,
      marginTop: theme.spacing.sm,
      borderTopWidth: 1,
    },
  });

export default CheckoutScreen;
