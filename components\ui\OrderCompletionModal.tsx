import { IconSymbol } from "@/components/ui/IconSymbol";
import { ModernButton } from "@/components/ui/ModernButton";
import { Spacing, Typography } from "@/constants/Design";
import { useThemeColor } from "@/hooks/useThemeColor";
import EnhancedThermalPrintService from "@/src/services/EnhancedThermalPrintService";
import { useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from "react-native";

export interface OrderCompletionAction {
  title: string;
  onPress: () => void;
  variant?: "primary" | "outline" | "ghost" | "danger";
  icon?: string;
}

interface OrderCompletionModalProps {
  visible: boolean;
  onClose: () => void;
  onComplete: () => void; // Called when the entire flow is complete and cart should be cleared
  orderData: any;
  orderNumber: string;
  orderTotal: number;
  paymentMethod: string;
  transactionId?: string;
  printingAlreadyAttempted?: boolean; // Whether printing was already attempted in the pre-order flow
  printingWasSuccessful?: boolean; // Whether the initial printing attempt was successful
}

type ModalStep =
  | "checking"
  | "printer_setup"
  | "printing"
  | "success"
  | "error";

export function OrderCompletionModal({
  visible,
  onClose,
  onComplete,
  orderData,
  orderNumber,
  orderTotal,
  paymentMethod,
  transactionId,
  printingAlreadyAttempted = false,
  printingWasSuccessful = false,
}: OrderCompletionModalProps) {
  const backgroundColor = useThemeColor({}, "surface");
  const textColor = useThemeColor({}, "text");
  const textSecondary = useThemeColor({}, "textSecondary");
  const primaryColor = useThemeColor({}, "primary");
  const borderColor = useThemeColor({}, "border");
  const successColor = "#10B981";
  const warningColor = "#F59E0B";
  const errorColor = "#EF4444";

  const router = useRouter();

  const [currentStep, setCurrentStep] = useState<ModalStep>("checking");
  const [printerAvailable, setPrinterAvailable] = useState(false);
  const [printingError, setPrintingError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    console.log("OrderCompletionModal useEffect - visible:", visible);
    console.log("OrderCompletionModal props:", {
      orderNumber,
      printingAlreadyAttempted,
      printingWasSuccessful,
    });
    if (visible) {
      startOrderCompletionFlow();
    }
  }, [visible]);

  const startOrderCompletionFlow = async () => {
    console.log("startOrderCompletionFlow called");
    console.log("printingAlreadyAttempted:", printingAlreadyAttempted);
    console.log("printingWasSuccessful:", printingWasSuccessful);

    // If printing was already attempted and successful, go directly to success
    if (printingAlreadyAttempted && printingWasSuccessful) {
      console.log("Going to success step");
      setCurrentStep("success");
      return;
    }

    // If printing was already attempted but failed, go to error state
    if (printingAlreadyAttempted && !printingWasSuccessful) {
      console.log("Going to error step");
      setCurrentStep("error");
      setPrintingError("Printing failed during order placement");
      return;
    }

    // If no printing was attempted yet, proceed with normal flow
    setCurrentStep("checking");
    setPrintingError(null);

    try {
      // Check if thermal printer is available
      const available =
        await EnhancedThermalPrintService.isThermalPrinterAvailable();
      setPrinterAvailable(available);

      if (available) {
        // Printer is available, proceed to automatic printing
        await attemptAutomaticPrinting();
      } else {
        // No printer available, show setup options
        setCurrentStep("printer_setup");
      }
    } catch (error) {
      console.error("Error in order completion flow:", error);
      setCurrentStep("printer_setup");
    }
  };

  const attemptAutomaticPrinting = async () => {
    setCurrentStep("printing");
    setIsProcessing(true);

    try {
      const printResult = await EnhancedThermalPrintService.printReceipt(
        orderData
      );

      if (printResult.success) {
        setCurrentStep("success");
      } else {
        setPrintingError(printResult.error || "Printing failed");
        setCurrentStep("error");
      }
    } catch (error) {
      console.error("Automatic printing error:", error);
      setPrintingError(
        error instanceof Error ? error.message : "Printing failed"
      );
      setCurrentStep("error");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSetupPrinter = () => {
    // Navigate to printer setup and close modal
    router.push("/thermal-printer-setup");
    handleComplete();
  };

  const handleSkipPrinting = () => {
    setCurrentStep("success");
  };

  const handlePrintLater = () => {
    // Save order for later printing (could implement offline storage here)
    Alert.alert(
      "Receipt Saved",
      "The receipt has been saved and can be printed later from the Orders screen.",
      [{ text: "OK", onPress: () => setCurrentStep("success") }]
    );
  };

  const handleRetryPrinting = async () => {
    await attemptAutomaticPrinting();
  };

  const handleComplete = () => {
    onComplete();
    onClose();
  };

  const handleViewOrders = () => {
    router.replace("/(tabs)/orders");
    handleComplete();
  };

  const handleNewSale = () => {
    router.replace("/(tabs)/products");
    handleComplete();
  };

  const handleViewReceipt = () => {
    router.push(`/order-receipt?orderNumber=${orderNumber}`);
    handleComplete();
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case "checking":
        return (
          <View style={styles.stepContainer}>
            <ActivityIndicator size="large" color={primaryColor} />
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Processing Order
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              Checking printer status...
            </Text>
          </View>
        );

      case "printer_setup":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: warningColor + "20" },
              ]}
            >
              <IconSymbol name="printer" size={32} color={warningColor} />
            </View>
            <Text style={[styles.stepTitle, { color: textColor }]}>
              No Printer Connected
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              Would you like to set up a thermal printer for automatic receipt
              printing?
            </Text>

            <View style={styles.actionsContainer}>
              <ModernButton
                title="Setup Printer"
                onPress={handleSetupPrinter}
                variant="primary"
                icon={<IconSymbol name="gear" size={16} color="white" />}
                style={styles.actionButton}
              />

              <ModernButton
                title="Print Later"
                onPress={handlePrintLater}
                variant="outline"
                icon={
                  <IconSymbol name="clock" size={16} color={primaryColor} />
                }
                style={styles.actionButton}
              />

              <ModernButton
                title="Skip Printing"
                onPress={handleSkipPrinting}
                variant="ghost"
                icon={
                  <IconSymbol name="xmark" size={16} color={textSecondary} />
                }
                style={styles.actionButton}
              />
            </View>
          </View>
        );

      case "printing":
        return (
          <View style={styles.stepContainer}>
            <ActivityIndicator size="large" color={primaryColor} />
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Printing Receipt
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              Please wait while we print your receipt...
            </Text>
          </View>
        );

      case "error":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: errorColor + "20" },
              ]}
            >
              <IconSymbol
                name="exclamationmark.triangle"
                size={32}
                color={errorColor}
              />
            </View>
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Printing Failed
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              {printingError ||
                "Unable to print receipt. You can try again or proceed without printing."}
            </Text>

            <View style={styles.actionsContainer}>
              <ModernButton
                title="Retry Printing"
                onPress={handleRetryPrinting}
                variant="primary"
                icon={
                  <IconSymbol name="arrow.clockwise" size={16} color="white" />
                }
                style={styles.actionButton}
                disabled={isProcessing}
              />

              <ModernButton
                title="Setup Printer"
                onPress={handleSetupPrinter}
                variant="outline"
                icon={<IconSymbol name="gear" size={16} color={primaryColor} />}
                style={styles.actionButton}
              />

              <ModernButton
                title="Continue Without Printing"
                onPress={handleSkipPrinting}
                variant="ghost"
                icon={
                  <IconSymbol
                    name="arrow.right"
                    size={16}
                    color={textSecondary}
                  />
                }
                style={styles.actionButton}
              />
            </View>
          </View>
        );

      case "success":
        return (
          <View style={styles.stepContainer}>
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: successColor + "20" },
              ]}
            >
              <IconSymbol
                name="checkmark.circle.fill"
                size={32}
                color={successColor}
              />
            </View>
            <Text style={[styles.stepTitle, { color: textColor }]}>
              Order Completed Successfully!
            </Text>
            <Text style={[styles.stepMessage, { color: textSecondary }]}>
              Order #{orderNumber} has been created and paid.{"\n\n"}
              Total: KSh {orderTotal.toFixed(2)}
              {"\n"}
              Payment: {paymentMethod}
              {transactionId ? `\nTransaction: ${transactionId}` : ""}
              {printingWasSuccessful
                ? "\n\nReceipt has been printed successfully!"
                : ""}
            </Text>

            <View style={styles.actionsContainer}>
              <ModernButton
                title="New Sale"
                onPress={handleNewSale}
                variant="primary"
                icon={<IconSymbol name="plus.circle" size={16} color="white" />}
                style={styles.actionButton}
              />

              {/* Only show Print Receipt button if printing wasn't already successful */}
              {!printingWasSuccessful && (
                <ModernButton
                  title="Print Receipt"
                  onPress={handleRetryPrinting}
                  variant="outline"
                  icon={
                    <IconSymbol name="printer" size={16} color={primaryColor} />
                  }
                  style={styles.actionButton}
                />
              )}

              <ModernButton
                title="View Receipt"
                onPress={handleViewReceipt}
                variant="outline"
                icon={
                  <IconSymbol name="doc.text" size={16} color={primaryColor} />
                }
                style={styles.actionButton}
              />

              <ModernButton
                title="View Orders"
                onPress={handleViewOrders}
                variant="outline"
                icon={
                  <IconSymbol
                    name="list.bullet"
                    size={16}
                    color={primaryColor}
                  />
                }
                style={styles.actionButton}
              />
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={() => {}} // Disable Android back button
    >
      <View style={styles.overlay}>
        <View style={[styles.modal, { backgroundColor, borderColor }]}>
          <ScrollView
            style={styles.scrollContainer}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            {renderStepContent()}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: Spacing.lg,
    zIndex: 9999, // Ensure modal is on top
    elevation: 9999, // Android elevation
  },
  modal: {
    width: "100%",
    maxWidth: 400,
    maxHeight: "80%", // Limit modal height to 80% of screen
    borderRadius: 16,
    borderWidth: 1,
    overflow: "hidden",
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: Spacing.lg,
    paddingBottom: Spacing.xl, // Extra padding at bottom for better scrolling
  },
  stepContainer: {
    alignItems: "center",
    paddingVertical: Spacing.md,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: Spacing.md,
  },
  stepTitle: {
    fontSize: Typography.h3.fontSize,
    fontWeight: Typography.h3.fontWeight as any,
    textAlign: "center",
    marginBottom: Spacing.sm,
  },
  stepMessage: {
    fontSize: Typography.body.fontSize,
    textAlign: "center",
    lineHeight: 22,
    marginBottom: Spacing.lg,
  },
  actionsContainer: {
    width: "100%",
    gap: Spacing.sm,
  },
  actionButton: {
    marginBottom: 0,
  },
});
