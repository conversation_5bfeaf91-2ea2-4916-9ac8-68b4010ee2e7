/**
 * Manual Credit Payment Test Script
 * 
 * This script tests the credit payment functionality to ensure
 * the lock timeout issue has been resolved.
 */

require('dotenv').config();
const PaymentTransactionService = require('../src/services/payment-transaction-service');
const { databaseManager } = require('../src/config/database-manager');

async function testCreditPayment() {
  console.log('🧪 Starting Credit Payment Test...');
  
  try {
    // Initialize database
    await databaseManager.initialize();
    console.log('✅ Database initialized');

    const paymentService = new PaymentTransactionService();

    // Step 1: Create a transaction with credit payment method
    console.log('\n📝 Step 1: Creating transaction with credit payment method...');
    
    const transactionData = {
      staffId: 'pos-001', // Use valid staff ID from pos_staff table
      customerId: 'gid://shopify/Customer/857174861',
      totalAmount: 2850.00,
      currency: 'KES',
      paymentMethods: [{
        methodType: 'credit',
        methodName: 'Credit Payment',
        amount: 2850.00
      }],
      notes: 'Test credit payment - Manual Script',
      metadata: {
        testRun: true,
        timestamp: new Date().toISOString()
      }
    };

    const transactionResult = await paymentService.initiateTransaction(transactionData);
    
    if (!transactionResult.success) {
      throw new Error(`Transaction initiation failed: ${transactionResult.error}`);
    }

    console.log('✅ Transaction created:', transactionResult.transactionId);

    if (!transactionResult.paymentMethods || transactionResult.paymentMethods.length === 0) {
      throw new Error('No payment methods returned from transaction creation');
    }

    console.log('✅ Payment method created:', transactionResult.paymentMethods[0].id);

    // Step 2: Process the credit payment
    console.log('\n💳 Step 2: Processing credit payment...');
    
    const processingData = {
      customerId: 'gid://shopify/Customer/857174861',
      customerName: 'Imani Customer',
      customerPhone: '254712345678',
      creditLimit: 50000,
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      paymentTerms: 'Net 30',
      notes: 'Manual Test Credit Payment - Script Test',
      existingBalance: 0
    };

    const methodId = transactionResult.paymentMethods[0].id;
    const processResult = await paymentService.processPaymentMethod(
      methodId,
      'pos-001', // Use valid staff ID
      processingData
    );

    if (!processResult.success) {
      throw new Error(`Payment processing failed: ${processResult.error}`);
    }

    console.log('✅ Credit payment processed successfully');
    console.log('✅ Payment status:', processResult.status);

    // Step 3: Verify transaction details
    console.log('\n🔍 Step 3: Verifying transaction details...');
    
    const transactionDetails = await paymentService.getTransactionDetails(transactionResult.transactionId);
    
    if (!transactionDetails.success) {
      throw new Error(`Failed to get transaction details: ${transactionDetails.error}`);
    }

    console.log('✅ Transaction status:', transactionDetails.transaction.payment_status);
    console.log('✅ Credit payments found:', transactionDetails.creditPayments.length);
    
    if (transactionDetails.creditPayments.length > 0) {
      const creditPayment = transactionDetails.creditPayments[0];
      console.log('✅ Credit payment details:');
      console.log('   - Customer:', creditPayment.customer_name);
      console.log('   - Amount:', creditPayment.outstanding_balance);
      console.log('   - Due date:', creditPayment.due_date);
      console.log('   - Status:', creditPayment.status);
    }

    // Step 4: Test concurrent processing (simulate multiple requests)
    console.log('\n🔄 Step 4: Testing concurrent credit payment processing...');
    
    const concurrentPromises = [];
    for (let i = 0; i < 3; i++) {
      concurrentPromises.push(testConcurrentCreditPayment(paymentService, i));
    }

    const concurrentResults = await Promise.allSettled(concurrentPromises);
    const successfulConcurrent = concurrentResults.filter(r => r.status === 'fulfilled').length;
    const failedConcurrent = concurrentResults.filter(r => r.status === 'rejected').length;

    console.log(`✅ Concurrent test results: ${successfulConcurrent} successful, ${failedConcurrent} failed`);

    if (failedConcurrent > 0) {
      console.log('❌ Some concurrent requests failed:');
      concurrentResults.forEach((result, index) => {
        if (result.status === 'rejected') {
          console.log(`   Request ${index}: ${result.reason.message}`);
        }
      });
    }

    // Cleanup
    console.log('\n🧹 Cleaning up test data...');
    await cleanupTestData(transactionResult.transactionId);
    console.log('✅ Cleanup completed');

    console.log('\n🎉 Credit Payment Test Completed Successfully!');
    console.log('✅ No lock timeout errors detected');
    console.log('✅ Transaction processing working correctly');

  } catch (error) {
    console.error('❌ Credit Payment Test Failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    // Close database connections
    if (databaseManager.pool) {
      await databaseManager.pool.end();
      console.log('✅ Database connections closed');
    }
  }
}

async function testConcurrentCreditPayment(paymentService, index) {
  const transactionData = {
    staffId: 'pos-001', // Use valid staff ID
    customerId: `test-customer-${index}`,
    totalAmount: 1000.00,
    currency: 'KES',
    paymentMethods: [{
      methodType: 'credit',
      methodName: 'Credit Payment',
      amount: 1000.00
    }],
    notes: `Concurrent test credit payment ${index}`,
    metadata: {
      testRun: true,
      concurrentIndex: index,
      timestamp: new Date().toISOString()
    }
  };

  const transactionResult = await paymentService.initiateTransaction(transactionData);
  
  const processingData = {
    customerId: `test-customer-${index}`,
    customerName: `Test Customer ${index}`,
    customerPhone: '254712345678',
    creditLimit: 50000,
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    paymentTerms: 'Net 30',
    notes: `Concurrent test credit payment ${index}`,
    existingBalance: 0
  };

  const methodId = transactionResult.paymentMethods[0].id;
  const processResult = await paymentService.processPaymentMethod(
    methodId,
    'pos-001', // Use valid staff ID
    processingData
  );

  // Cleanup concurrent test data
  await cleanupTestData(transactionResult.transactionId);

  return processResult;
}

async function cleanupTestData(transactionId) {
  try {
    await databaseManager.executeQuery(
      'DELETE FROM credit_payments WHERE payment_method_id IN (SELECT id FROM payment_methods_used WHERE transaction_id = ?)',
      [transactionId]
    );
    await databaseManager.executeQuery(
      'DELETE FROM payment_methods_used WHERE transaction_id = ?',
      [transactionId]
    );
    await databaseManager.executeQuery(
      'DELETE FROM payment_transactions WHERE id = ?',
      [transactionId]
    );
  } catch (error) {
    console.warn('⚠️ Cleanup warning:', error.message);
  }
}

// Run the test
if (require.main === module) {
  testCreditPayment();
}

module.exports = { testCreditPayment };
