# Server Configuration
PORT=3020
# NODE_ENV options: development (for local dev), production (for live), test (for testing)
NODE_ENV=development

# Shopify App Configuration
SHOPIFY_API_KEY=your_shopify_api_key_here
SHOPIFY_API_SECRET=your_shopify_api_secret_here
SHOPIFY_SCOPES=read_products,write_orders,read_customers,write_customers,read_inventory,write_inventory
SHOPIFY_APP_URL=http://localhost:3020

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=24h

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,exp://*************:8081

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# MySQL Database Configuration (Required for migration)
DB_HOST=localhost
DB_PORT=3306
DB_USER=dukalink
DB_PASSWORD=your_secure_password_here
DB_NAME=dukalink_pos
DB_CHARSET=utf8mb4

# Database connection pool settings (Centralized Pool Configuration)
DB_CONNECTION_LIMIT=20
DB_QUEUE_LIMIT=0
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000
DB_IDLE_TIMEOUT=300000
DB_HEALTH_CHECK_INTERVAL=30000

# MySQL-specific timeout settings (to prevent lock timeouts)
# These should be set in your MySQL configuration as well
DB_LOCK_WAIT_TIMEOUT=50
DB_INNODB_LOCK_WAIT_TIMEOUT=50
DB_TRANSACTION_ISOLATION=READ_COMMITTED

# Database SSL settings (for production)
DB_SSL=false
DB_SSL_REJECT_UNAUTHORIZED=true

# Test database (for running tests)
TEST_DB_NAME=dukalink_pos_test
TEST_DB_USER=dukalink_test
TEST_DB_PASSWORD=test_password

# Migration settings
ENABLE_MOCK_DATA=false
MIGRATION_MODE=false

# Legacy settings (keep for backward compatibility)
DATABASE_URL=postgresql://username:password@localhost:5432/dukalink_pos
REDIS_URL=redis://localhost:6379
