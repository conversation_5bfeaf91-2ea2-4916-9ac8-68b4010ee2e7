/**
 * Modern PIN Verification Modal Component
 *
 * Clean design without header/icon, containerized user section with avatar,
 * compact switch button, and proper theme font integration.
 */

import { useSession } from "@/src/contexts/AuthContext";
import { useTheme } from "@/src/contexts/ThemeContext";
import { useUserPreferences } from "@/src/contexts/UserPreferencesContext";
import {
  useUserSwitching,
  type AvailableStaff,
} from "@/src/contexts/UserSwitchingContext";
import { Ionicons } from "@expo/vector-icons";
import React, { useEffect, useState, useRef, useCallback } from "react";
import {
  ActivityIndicator,
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
  TextInput,
  Dimensions,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
} from "react-native";

const { width: screenWidth, height: screenHeight } = Dimensions.get("window");

// Responsive breakpoints for web platform
const isWeb = Platform.OS === "web";
const isSmallScreen = screenHeight < 600;
const isMediumScreen = screenHeight >= 600 && screenHeight < 800;
const isLargeScreen = screenHeight >= 800;

// Dynamic modal sizing based on screen size and platform
const getModalMaxHeight = () => {
  if (isWeb) {
    if (isSmallScreen) return screenHeight * 0.95; // Use more space on small screens
    if (isMediumScreen) return screenHeight * 0.85;
    return screenHeight * 0.8; // Large screens can afford more padding
  }
  // Mobile: Use percentage-based height (80% of screen height)
  return "80%"; // Much cleaner and responsive on mobile
};

const getModalWidth = () => {
  if (isWeb) {
    return Math.min(screenWidth * 0.9, 420); // Keep web behavior as is
  }
  // Mobile: Use more width and ensure minimum width
  return Math.max(screenWidth * 0.95, 320); // Use more screen width on mobile
};

type ModalState = "pin_current" | "user_selection" | "pin_selected";

interface PinVerificationModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  title?: string;
  description?: string;
  allowUserSwitching?: boolean;
}

export const PinVerificationModal: React.FC<PinVerificationModalProps> = ({
  visible,
  onClose,
  onSuccess,
  title = "PIN Verification Required",
  description = "Enter your PIN to access Products",
  allowUserSwitching = true,
}) => {
  const theme = useTheme();
  const { user } = useSession();
  const { preferences, updatePreference } = useUserPreferences();
  const {
    validatePin,
    switchUser,
    availableStaff,
    loadAvailableStaff,
    loadSessionContext,
    error: contextError,
  } = useUserSwitching();

  // Refs for physical keyboard input
  const physicalInputRef = useRef<TextInput>(null);

  const [currentState, setCurrentState] = useState<ModalState>("pin_current");
  const [pin, setPin] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [error, setError] = useState("");
  const [selectedStaff, setSelectedStaff] = useState<AvailableStaff | null>(
    null
  );
  const [searchQuery, setSearchQuery] = useState("");
  const [persistentError, setPersistentError] = useState(""); // New state for persistent errors

  // Get current keyboard mode from preferences
  const keyboardMode = preferences.pinKeyboardMode;
  const isPhysicalMode = keyboardMode === "physical";

  const handlePinComplete = useCallback(
    async (enteredPin: string) => {
      try {
        setIsVerifying(true);
        setError("");
        setPersistentError("");

        if (currentState === "pin_current") {
          // Current user PIN verification
          if (!user?.id) {
            const errorMsg = "User not found";
            setError(errorMsg);
            setPersistentError(errorMsg);
            setPin("");
            return;
          }

          // Check if current user has a PIN before attempting validation
          if (!user.has_pin) {
            const errorMsg =
              "You don't have a PIN set up. Please contact an administrator or switch to a user with a PIN.";
            setError(errorMsg);
            setPersistentError(errorMsg);
            setPin("");
            return;
          }

          const isValid = await validatePin(user.id, enteredPin);
          if (isValid) {
            // Small delay to ensure UI updates
            setTimeout(() => {
              onSuccess();
              onClose();
            }, 100);
          } else {
            // Use the specific error from UserSwitchingContext
            const errorMsg = contextError || "Invalid PIN. Please try again.";
            setError(errorMsg);
            setPersistentError(errorMsg);
            setPin("");
          }
        } else if (currentState === "pin_selected" && selectedStaff) {
          // User switching
          const success = await switchUser(
            selectedStaff.id,
            enteredPin,
            "manual_switch"
          );
          if (success) {
            // Give time for user context to update, then call success
            setTimeout(() => {
              onSuccess();
              onClose();
            }, 200);
          } else {
            // Use the specific error from UserSwitchingContext
            const errorMsg = contextError || "Invalid PIN. Please try again.";
            setError(errorMsg);
            setPersistentError(errorMsg);
            setPin("");
          }
        }
      } catch (err) {
        console.error("PIN verification error:", err);
        const errorMsg = "Verification failed. Please try again.";
        setError(errorMsg);
        setPersistentError(errorMsg);
        setPin("");
      } finally {
        setIsVerifying(false);
      }
    },
    [
      currentState,
      user,
      selectedStaff,
      validatePin,
      switchUser,
      contextError,
      onSuccess,
      onClose,
    ]
  );

  // Reset state when modal opens/closes
  useEffect(() => {
    if (visible) {
      setCurrentState("pin_current");
      setPin("");
      setError("");
      setPersistentError("");
      setIsVerifying(false);
      setSelectedStaff(null);
      setSearchQuery("");

      // Refresh session context to ensure we show the correct current user
      // Only call this once when modal opens, not on every render
      loadSessionContext();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]); // Only depend on visible to avoid re-renders

  // Clear error only when user starts typing a new PIN (not when PIN is cleared after error)
  useEffect(() => {
    if (pin.length > 0 && (error || persistentError)) {
      setError("");
      setPersistentError("");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pin.length]); // Only depend on pin.length to avoid infinite loops

  // Load available staff when switching to user selection
  useEffect(() => {
    if (currentState === "user_selection") {
      loadAvailableStaff();
    }
  }, [currentState, loadAvailableStaff]);

  // Update persistent error when context error changes
  useEffect(() => {
    if (contextError && !isVerifying) {
      setPersistentError(contextError);
    }
  }, [contextError, isVerifying]);

  // Handle physical keyboard focus when mode changes or modal opens
  useEffect(() => {
    if (visible && isPhysicalMode && currentState === "pin_current") {
      setTimeout(() => {
        physicalInputRef.current?.focus();
      }, 300); // Delay to ensure modal is fully rendered
    }
  }, [visible, isPhysicalMode, currentState]);

  // Function definitions for keyboard handling using useCallback
  const handleClearPin = useCallback(() => {
    if (!isVerifying) {
      setPin("");
      setError("");
      setPersistentError("");
    }
  }, [isVerifying]);

  const handleClose = useCallback(() => {
    if (!isVerifying) {
      onClose();
    }
  }, [isVerifying, onClose]);

  const handleKeypadPress = useCallback(
    (digit: number) => {
      if (pin.length < 4 && !isVerifying) {
        const newPin = pin + digit.toString();
        setPin(newPin);

        // Auto-submit when 4 digits are entered
        if (newPin.length === 4) {
          setTimeout(() => handlePinComplete(newPin), 100);
        }
      }
    },
    [pin, isVerifying, handlePinComplete]
  );

  // Web-specific keyboard navigation support and accessibility
  useEffect(() => {
    if (!isWeb || !visible) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Handle numeric keys (0-9)
      if (event.key >= "0" && event.key <= "9" && !isPhysicalMode) {
        event.preventDefault();
        handleKeypadPress(parseInt(event.key));
      }

      // Handle backspace/delete
      if (
        (event.key === "Backspace" || event.key === "Delete") &&
        !isPhysicalMode
      ) {
        event.preventDefault();
        handleClearPin();
      }

      // Handle Enter key for submission
      if (event.key === "Enter" && pin.length === 4) {
        event.preventDefault();
        handlePinComplete(pin);
      }

      // Handle Escape key for closing
      if (event.key === "Escape") {
        event.preventDefault();
        handleClose();
      }
    };

    // Prevent body scroll when modal is open
    document.body.style.overflow = "hidden";

    // Add keyboard event listener
    document.addEventListener("keydown", handleKeyDown);

    // Focus management for accessibility
    const modalElement = document.querySelector(
      '[data-modal="pin-verification"]'
    );
    if (modalElement) {
      (modalElement as HTMLElement).focus();
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "unset";
    };
  }, [
    visible,
    pin,
    isPhysicalMode,
    isVerifying,
    handleKeypadPress,
    handleClearPin,
    handlePinComplete,
    handleClose,
  ]);

  const handlePhysicalKeyboardInput = (text: string) => {
    // Only allow numeric input and limit to 4 digits
    const numericText = text.replace(/[^0-9]/g, "").slice(0, 4);
    setPin(numericText);

    // Auto-submit when 4 digits are entered
    if (numericText.length === 4) {
      setTimeout(() => handlePinComplete(numericText), 100);
    }
  };

  const toggleKeyboardMode = async () => {
    const newMode = keyboardMode === "onscreen" ? "physical" : "onscreen";
    await updatePreference("pinKeyboardMode", newMode);

    // Focus physical input if switching to physical mode
    if (newMode === "physical") {
      setTimeout(() => {
        physicalInputRef.current?.focus();
      }, 100);
    } else {
      // Dismiss keyboard if switching to onscreen mode
      Keyboard.dismiss();
    }
  };

  const handleSwitchUser = () => {
    if (!isVerifying && allowUserSwitching) {
      setCurrentState("user_selection");
      setPin("");
      setError("");
      setPersistentError("");
    }
  };

  const handleStaffSelect = (staff: AvailableStaff) => {
    if (!staff.has_pin) {
      setError(`${staff.name} does not have a PIN set up.`);
      return;
    }
    setSelectedStaff(staff);
    setCurrentState("pin_selected");
    setPin("");
    setError("");
    setPersistentError("");
  };

  const handleBackToPinCurrent = () => {
    setCurrentState("pin_current");
    setSelectedStaff(null);
    setPin("");
    setError("");
    setPersistentError("");
  };

  // Filter staff based on search query
  const filteredStaff = availableStaff.filter(
    (staff) =>
      staff.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      staff.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
      staff.role.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Debug modal sizing on mobile
  React.useEffect(() => {
    if (visible && !isWeb) {
      console.log("📱 PIN Modal Debug:", {
        screenHeight,
        screenWidth,
        modalMaxHeight: getModalMaxHeight(),
        modalWidth: getModalWidth(),
        platform: Platform.OS,
      });
    }
  }, [visible]);

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent
      onRequestClose={handleClose}
      statusBarTranslucent
      presentationStyle="overFullScreen"
    >
      <KeyboardAvoidingView
        style={styles.overlay}
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
      >
        <View
          style={[
            styles.container,
            {
              width: getModalWidth(),
              ...(isWeb
                ? { maxHeight: getModalMaxHeight() }
                : { height: "80%" }), // Use height instead of maxHeight for mobile
              backgroundColor: theme.colors.surface,
              borderColor: theme.colors.border,
            },
          ]}
          {...(isWeb && { "data-modal": "pin-verification", tabIndex: -1 })}
        >
          {/* Header with Keyboard Toggle and Close Button */}
          {!isVerifying && (
            <View style={styles.modalHeader}>
              {/* Keyboard Mode Toggle - Left */}
              <TouchableOpacity
                style={[
                  styles.headerButton,
                  { backgroundColor: theme.colors.surfaceSecondary },
                ]}
                onPress={toggleKeyboardMode}
                activeOpacity={0.7}
              >
                <Ionicons
                  name={
                    keyboardMode === "onscreen" ? "keypad" : "hardware-chip"
                  }
                  size={18}
                  color={theme.colors.textSecondary}
                />
              </TouchableOpacity>

              {/* Close Button - Right */}
              <TouchableOpacity
                style={[
                  styles.headerButton,
                  { backgroundColor: theme.colors.surfaceSecondary },
                ]}
                onPress={handleClose}
                activeOpacity={0.7}
              >
                <Ionicons
                  name="close"
                  size={18}
                  color={theme.colors.textSecondary}
                />
              </TouchableOpacity>
            </View>
          )}

          {/* Scrollable Content Container */}
          <ScrollView
            style={styles.scrollContainer}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={isWeb}
            keyboardShouldPersistTaps="handled"
          >
            {/* Render content based on current state */}
            {currentState === "pin_current" && renderPinCurrentState()}
            {currentState === "user_selection" && renderUserSelectionState()}
            {currentState === "pin_selected" && renderPinSelectedState()}
          </ScrollView>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );

  // Render functions for different states
  function renderPinCurrentState() {
    return (
      <>
        {/* Title */}
        <Text
          style={[
            styles.title,
            {
              color: theme.colors.text,
              fontFamily: theme.typography.h1.fontFamily,
              fontSize: theme.typography.h1.fontSize,
            },
          ]}
        >
          {title}
        </Text>
        <Text
          style={[
            styles.description,
            {
              color: theme.colors.textSecondary,
              fontFamily: theme.typography.body.fontFamily,
              fontSize: theme.typography.body.fontSize,
            },
          ]}
        >
          {description}
        </Text>

        {/* User Container */}
        <TouchableOpacity
          style={[
            styles.userContainer,
            {
              backgroundColor: theme.colors.surfaceSecondary,
              borderColor: theme.colors.border,
            },
          ]}
          onPress={handleSwitchUser}
          activeOpacity={0.7}
          disabled={!allowUserSwitching || isVerifying}
        >
          <View style={styles.userRow}>
            {/* Avatar */}
            <View
              style={[styles.avatar, { backgroundColor: theme.colors.primary }]}
            >
              <Text
                style={[
                  styles.avatarText,
                  {
                    color: theme.colors.primaryForeground,
                    fontFamily: theme.typography.buttonLarge.fontFamily,
                  },
                ]}
              >
                {(user?.name || "U").charAt(0).toUpperCase()}
              </Text>
            </View>

            {/* User Info */}
            <View style={styles.userInfo}>
              <Text
                style={[
                  styles.userName,
                  {
                    color: theme.colors.text,
                    fontFamily: theme.typography.bodyLarge.fontFamily,
                    fontSize: theme.typography.bodyLarge.fontSize,
                  },
                ]}
              >
                {user?.name || "Current User"}
              </Text>
              {allowUserSwitching && !isVerifying && (
                <Text
                  style={[
                    styles.switchLabel,
                    {
                      color: theme.colors.textSecondary,
                      fontFamily: theme.typography.caption.fontFamily,
                      fontSize: theme.typography.caption.fontSize,
                    },
                  ]}
                >
                  Switch Account
                </Text>
              )}
            </View>

            {/* Large Chevron */}
            {allowUserSwitching && !isVerifying && (
              <View style={styles.chevronContainer}>
                <Ionicons
                  name="chevron-forward"
                  size={20}
                  color={theme.colors.textSecondary}
                />
              </View>
            )}
          </View>
        </TouchableOpacity>

        {/* PIN Input */}
        <View style={styles.pinSection}>
          {/* Hidden TextInput for physical keyboard mode */}
          {isPhysicalMode && (
            <TextInput
              ref={physicalInputRef}
              style={styles.hiddenInput}
              value={pin}
              onChangeText={handlePhysicalKeyboardInput}
              keyboardType="numeric"
              maxLength={4}
              autoFocus={isPhysicalMode}
              secureTextEntry={false}
              editable={!isVerifying}
            />
          )}

          <View style={styles.pinBoxes}>
            {[0, 1, 2, 3].map((index) => (
              <View
                key={index}
                style={[
                  styles.pinBox,
                  {
                    borderColor:
                      error || persistentError
                        ? theme.colors.error
                        : theme.colors.border,
                    backgroundColor: theme.colors.background,
                  },
                ]}
              >
                {pin[index] ? (
                  <View
                    style={[
                      styles.pinDot,
                      { backgroundColor: theme.colors.text },
                    ]}
                  />
                ) : null}
              </View>
            ))}
          </View>

          {/* Keyboard Mode Indicator */}
          {isPhysicalMode && (
            <Text
              style={[
                styles.keyboardModeText,
                {
                  color: theme.colors.textSecondary,
                  fontFamily: theme.typography.caption.fontFamily,
                  fontSize: theme.typography.caption.fontSize,
                },
              ]}
            >
              Using physical keyboard
            </Text>
          )}

          {/* Progress and Error Messages */}
          {isVerifying && (
            <View style={styles.progressContainer}>
              <ActivityIndicator size="small" color={theme.colors.primary} />
              <Text
                style={[
                  styles.progressText,
                  {
                    color: theme.colors.textSecondary,
                    fontFamily: theme.typography.caption.fontFamily,
                    fontSize: theme.typography.caption.fontSize,
                  },
                ]}
              >
                Verifying PIN...
              </Text>
            </View>
          )}

          {(error || persistentError) && !isVerifying && (
            <Text
              style={[
                styles.errorText,
                {
                  color: theme.colors.error,
                  fontFamily: theme.typography.caption.fontFamily,
                  fontSize: theme.typography.caption.fontSize,
                },
              ]}
            >
              {error || persistentError}
            </Text>
          )}
        </View>

        {/* Compact Keypad - Only show in onscreen mode */}
        {!isPhysicalMode && (
          <View style={styles.keypad}>
            {/* Row 1 */}
            <View style={styles.keypadRow}>
              {[1, 2, 3].map((num) => (
                <TouchableOpacity
                  key={num}
                  style={[
                    styles.keypadButton,
                    styles.keypadButtonShadow,
                    {
                      backgroundColor: theme.colors.surface,
                      borderColor: theme.colors.border,
                    },
                  ]}
                  onPress={() => handleKeypadPress(num)}
                  activeOpacity={0.7}
                  disabled={isVerifying}
                >
                  <Text
                    style={[
                      styles.keypadText,
                      {
                        color: theme.colors.text,
                        fontFamily: theme.typography.buttonLarge.fontFamily,
                        fontSize: theme.typography.buttonLarge.fontSize,
                      },
                    ]}
                  >
                    {num}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Row 2 */}
            <View style={styles.keypadRow}>
              {[4, 5, 6].map((num) => (
                <TouchableOpacity
                  key={num}
                  style={[
                    styles.keypadButton,
                    styles.keypadButtonShadow,
                    {
                      backgroundColor: theme.colors.surface,
                      borderColor: theme.colors.border,
                    },
                  ]}
                  onPress={() => handleKeypadPress(num)}
                  activeOpacity={0.7}
                  disabled={isVerifying}
                >
                  <Text
                    style={[
                      styles.keypadText,
                      {
                        color: theme.colors.text,
                        fontFamily: theme.typography.buttonLarge.fontFamily,
                        fontSize: theme.typography.buttonLarge.fontSize,
                      },
                    ]}
                  >
                    {num}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Row 3 */}
            <View style={styles.keypadRow}>
              {[7, 8, 9].map((num) => (
                <TouchableOpacity
                  key={num}
                  style={[
                    styles.keypadButton,
                    styles.keypadButtonShadow,
                    {
                      backgroundColor: theme.colors.surface,
                      borderColor: theme.colors.border,
                    },
                  ]}
                  onPress={() => handleKeypadPress(num)}
                  activeOpacity={0.7}
                  disabled={isVerifying}
                >
                  <Text
                    style={[
                      styles.keypadText,
                      {
                        color: theme.colors.text,
                        fontFamily: theme.typography.buttonLarge.fontFamily,
                        fontSize: theme.typography.buttonLarge.fontSize,
                      },
                    ]}
                  >
                    {num}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Row 4 */}
            <View style={styles.keypadRow}>
              {/* Clear */}
              <TouchableOpacity
                style={[
                  styles.keypadButton,
                  styles.keypadButtonShadow,
                  {
                    backgroundColor: theme.colors.surfaceSecondary,
                    borderColor: theme.colors.border,
                  },
                ]}
                onPress={handleClearPin}
                activeOpacity={0.7}
                disabled={isVerifying}
              >
                <Ionicons
                  name="close"
                  size={18}
                  color={theme.colors.textSecondary}
                />
              </TouchableOpacity>

              {/* 0 */}
              <TouchableOpacity
                style={[
                  styles.keypadButton,
                  styles.keypadButtonShadow,
                  {
                    backgroundColor: theme.colors.surface,
                    borderColor: theme.colors.border,
                  },
                ]}
                onPress={() => handleKeypadPress(0)}
                activeOpacity={0.7}
                disabled={isVerifying}
              >
                <Text
                  style={[
                    styles.keypadText,
                    {
                      color: theme.colors.text,
                      fontFamily: theme.typography.buttonLarge.fontFamily,
                      fontSize: theme.typography.buttonLarge.fontSize,
                    },
                  ]}
                >
                  0
                </Text>
              </TouchableOpacity>

              {/* Submit */}
              <TouchableOpacity
                style={[
                  styles.keypadButton,
                  styles.keypadButtonShadow,
                  {
                    backgroundColor:
                      pin.length === 4
                        ? theme.colors.primary
                        : theme.colors.surfaceSecondary,
                    borderColor:
                      pin.length === 4
                        ? theme.colors.primary
                        : theme.colors.border,
                  },
                ]}
                onPress={() => pin.length === 4 && handlePinComplete(pin)}
                activeOpacity={0.7}
                disabled={isVerifying || pin.length !== 4}
              >
                <Ionicons
                  name="arrow-forward"
                  size={18}
                  color={
                    pin.length === 4
                      ? theme.colors.primaryForeground
                      : theme.colors.textSecondary
                  }
                />
              </TouchableOpacity>
            </View>
          </View>
        )}
      </>
    );
  }

  function renderUserSelectionState() {
    return (
      <>
        {/* Title */}
        <Text
          style={[
            styles.title,
            {
              color: theme.colors.text,
              fontFamily: theme.typography.h1.fontFamily,
              fontSize: theme.typography.h1.fontSize,
            },
          ]}
        >
          Switch User
        </Text>
        <Text
          style={[
            styles.description,
            {
              color: theme.colors.textSecondary,
              fontFamily: theme.typography.body.fontFamily,
              fontSize: theme.typography.body.fontSize,
            },
          ]}
        >
          Select a user to switch to
        </Text>

        {/* Search Input */}
        <View style={styles.searchSection}>
          <View
            style={[
              styles.searchContainer,
              { borderColor: theme.colors.border },
            ]}
          >
            <Ionicons
              name="search"
              size={16}
              color={theme.colors.textSecondary}
            />
            <TextInput
              style={[styles.searchInput, { color: theme.colors.text }]}
              placeholder="Search staff..."
              placeholderTextColor={theme.colors.textSecondary}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>
        </View>

        {/* Staff List */}
        <ScrollView
          style={styles.staffList}
          showsVerticalScrollIndicator={false}
        >
          {filteredStaff.map((staff) => (
            <TouchableOpacity
              key={staff.id}
              style={[
                styles.staffItem,
                {
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.border,
                  opacity: staff.has_pin ? 1 : 0.5,
                },
              ]}
              onPress={() => handleStaffSelect(staff)}
              disabled={!staff.has_pin}
            >
              <View style={styles.staffInfo}>
                <Text style={[styles.staffName, { color: theme.colors.text }]}>
                  {staff.name}
                </Text>
                <Text
                  style={[
                    styles.staffRole,
                    { color: theme.colors.textSecondary },
                  ]}
                >
                  {staff.role} • {staff.username}
                </Text>
              </View>
              <View style={styles.staffStatus}>
                {staff.has_pin ? (
                  <View
                    style={[styles.pinBadge, { backgroundColor: "#10B981" }]}
                  >
                    <Ionicons name="checkmark" size={10} color="white" />
                    <Text style={styles.pinBadgeText}>PIN</Text>
                  </View>
                ) : (
                  <View
                    style={[styles.pinBadge, { backgroundColor: "#EF4444" }]}
                  >
                    <Ionicons name="close" size={10} color="white" />
                    <Text style={styles.pinBadgeText}>No PIN</Text>
                  </View>
                )}
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Back Button */}
        <TouchableOpacity
          style={[styles.backButton, { borderColor: theme.colors.border }]}
          onPress={handleBackToPinCurrent}
          activeOpacity={0.7}
        >
          <Ionicons
            name="arrow-back"
            size={16}
            color={theme.colors.textSecondary}
          />
          <Text
            style={[
              styles.backButtonText,
              { color: theme.colors.textSecondary },
            ]}
          >
            Back to PIN Entry
          </Text>
        </TouchableOpacity>
      </>
    );
  }

  function renderPinSelectedState() {
    return (
      <>
        {/* Title */}
        <Text
          style={[
            styles.title,
            {
              color: theme.colors.text,
              fontFamily: theme.typography.h1.fontFamily,
              fontSize: theme.typography.h1.fontSize,
            },
          ]}
        >
          Enter PIN
        </Text>
        <Text
          style={[
            styles.description,
            {
              color: theme.colors.textSecondary,
              fontFamily: theme.typography.body.fontFamily,
              fontSize: theme.typography.body.fontSize,
            },
          ]}
        >
          Enter PIN for {selectedStaff?.name}
        </Text>

        {/* Selected User Container */}
        <View
          style={[
            styles.userContainer,
            {
              backgroundColor: theme.colors.surfaceSecondary,
              borderColor: theme.colors.border,
            },
          ]}
        >
          <View style={styles.userRow}>
            {/* Avatar */}
            <View
              style={[styles.avatar, { backgroundColor: theme.colors.primary }]}
            >
              <Text
                style={[
                  styles.avatarText,
                  {
                    color: theme.colors.primaryForeground,
                    fontFamily: theme.typography.buttonLarge.fontFamily,
                  },
                ]}
              >
                {(selectedStaff?.name || "U").charAt(0).toUpperCase()}
              </Text>
            </View>

            {/* User Info */}
            <View style={styles.userInfo}>
              <Text
                style={[
                  styles.userName,
                  {
                    color: theme.colors.text,
                    fontFamily: theme.typography.bodyLarge.fontFamily,
                    fontSize: theme.typography.bodyLarge.fontSize,
                  },
                ]}
              >
                {selectedStaff?.name}
              </Text>
              <Text
                style={[
                  styles.switchLabel,
                  {
                    color: theme.colors.textSecondary,
                    fontFamily: theme.typography.caption.fontFamily,
                    fontSize: theme.typography.caption.fontSize,
                  },
                ]}
              >
                Switching to this user
              </Text>
            </View>

            {/* Back Button */}
            <TouchableOpacity
              style={[
                styles.chevronContainer,
                { borderColor: theme.colors.border },
              ]}
              onPress={() => setCurrentState("user_selection")}
              activeOpacity={0.7}
            >
              <Ionicons
                name="arrow-back"
                size={16}
                color={theme.colors.textSecondary}
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* PIN Input */}
        <View style={styles.pinSection}>
          {/* Hidden TextInput for physical keyboard mode */}
          {isPhysicalMode && (
            <TextInput
              ref={physicalInputRef}
              style={styles.hiddenInput}
              value={pin}
              onChangeText={handlePhysicalKeyboardInput}
              keyboardType="numeric"
              maxLength={4}
              autoFocus={isPhysicalMode}
              secureTextEntry={false}
              editable={!isVerifying}
            />
          )}

          <View style={styles.pinBoxes}>
            {[0, 1, 2, 3].map((index) => (
              <View
                key={index}
                style={[
                  styles.pinBox,
                  {
                    borderColor:
                      error || persistentError
                        ? theme.colors.error
                        : theme.colors.border,
                    backgroundColor: theme.colors.background,
                  },
                ]}
              >
                {pin[index] ? (
                  <View
                    style={[
                      styles.pinDot,
                      { backgroundColor: theme.colors.text },
                    ]}
                  />
                ) : null}
              </View>
            ))}
          </View>

          {/* Keyboard Mode Indicator */}
          {isPhysicalMode && (
            <Text
              style={[
                styles.keyboardModeText,
                {
                  color: theme.colors.textSecondary,
                  fontFamily: theme.typography.caption.fontFamily,
                  fontSize: theme.typography.caption.fontSize,
                },
              ]}
            >
              Using physical keyboard
            </Text>
          )}

          {/* Progress and Error Messages */}
          {isVerifying && (
            <View style={styles.progressContainer}>
              <ActivityIndicator size="small" color={theme.colors.primary} />
              <Text
                style={[
                  styles.progressText,
                  {
                    color: theme.colors.textSecondary,
                    fontFamily: theme.typography.caption.fontFamily,
                    fontSize: theme.typography.caption.fontSize,
                  },
                ]}
              >
                Switching user...
              </Text>
            </View>
          )}

          {(error || persistentError) && !isVerifying && (
            <Text
              style={[
                styles.errorText,
                {
                  color: theme.colors.error,
                  fontFamily: theme.typography.caption.fontFamily,
                  fontSize: theme.typography.caption.fontSize,
                },
              ]}
            >
              {error || persistentError}
            </Text>
          )}
        </View>

        {/* Compact Keypad - Only show in onscreen mode */}
        {!isPhysicalMode && (
          <View style={styles.keypad}>
            {/* Row 1 */}
            <View style={styles.keypadRow}>
              {[1, 2, 3].map((num) => (
                <TouchableOpacity
                  key={num}
                  style={[
                    styles.keypadButton,
                    styles.keypadButtonShadow,
                    {
                      backgroundColor: theme.colors.surface,
                      borderColor: theme.colors.border,
                    },
                  ]}
                  onPress={() => handleKeypadPress(num)}
                  activeOpacity={0.7}
                  disabled={isVerifying}
                >
                  <Text
                    style={[
                      styles.keypadText,
                      {
                        color: theme.colors.text,
                        fontFamily: theme.typography.buttonLarge.fontFamily,
                        fontSize: theme.typography.buttonLarge.fontSize,
                      },
                    ]}
                  >
                    {num}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Row 2 */}
            <View style={styles.keypadRow}>
              {[4, 5, 6].map((num) => (
                <TouchableOpacity
                  key={num}
                  style={[
                    styles.keypadButton,
                    styles.keypadButtonShadow,
                    {
                      backgroundColor: theme.colors.surface,
                      borderColor: theme.colors.border,
                    },
                  ]}
                  onPress={() => handleKeypadPress(num)}
                  activeOpacity={0.7}
                  disabled={isVerifying}
                >
                  <Text
                    style={[
                      styles.keypadText,
                      {
                        color: theme.colors.text,
                        fontFamily: theme.typography.buttonLarge.fontFamily,
                        fontSize: theme.typography.buttonLarge.fontSize,
                      },
                    ]}
                  >
                    {num}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Row 3 */}
            <View style={styles.keypadRow}>
              {[7, 8, 9].map((num) => (
                <TouchableOpacity
                  key={num}
                  style={[
                    styles.keypadButton,
                    styles.keypadButtonShadow,
                    {
                      backgroundColor: theme.colors.surface,
                      borderColor: theme.colors.border,
                    },
                  ]}
                  onPress={() => handleKeypadPress(num)}
                  activeOpacity={0.7}
                  disabled={isVerifying}
                >
                  <Text
                    style={[
                      styles.keypadText,
                      {
                        color: theme.colors.text,
                        fontFamily: theme.typography.buttonLarge.fontFamily,
                        fontSize: theme.typography.buttonLarge.fontSize,
                      },
                    ]}
                  >
                    {num}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Row 4 */}
            <View style={styles.keypadRow}>
              {/* Clear */}
              <TouchableOpacity
                style={[
                  styles.keypadButton,
                  styles.keypadButtonShadow,
                  {
                    backgroundColor: theme.colors.surfaceSecondary,
                    borderColor: theme.colors.border,
                  },
                ]}
                onPress={handleClearPin}
                activeOpacity={0.7}
                disabled={isVerifying}
              >
                <Ionicons
                  name="close"
                  size={18}
                  color={theme.colors.textSecondary}
                />
              </TouchableOpacity>

              {/* 0 */}
              <TouchableOpacity
                style={[
                  styles.keypadButton,
                  styles.keypadButtonShadow,
                  {
                    backgroundColor: theme.colors.surface,
                    borderColor: theme.colors.border,
                  },
                ]}
                onPress={() => handleKeypadPress(0)}
                activeOpacity={0.7}
                disabled={isVerifying}
              >
                <Text
                  style={[
                    styles.keypadText,
                    {
                      color: theme.colors.text,
                      fontFamily: theme.typography.buttonLarge.fontFamily,
                      fontSize: theme.typography.buttonLarge.fontSize,
                    },
                  ]}
                >
                  0
                </Text>
              </TouchableOpacity>

              {/* Submit */}
              <TouchableOpacity
                style={[
                  styles.keypadButton,
                  styles.keypadButtonShadow,
                  {
                    backgroundColor:
                      pin.length === 4
                        ? theme.colors.primary
                        : theme.colors.surfaceSecondary,
                    borderColor:
                      pin.length === 4
                        ? theme.colors.primary
                        : theme.colors.border,
                  },
                ]}
                onPress={() => pin.length === 4 && handlePinComplete(pin)}
                activeOpacity={0.7}
                disabled={isVerifying || pin.length !== 4}
              >
                <Ionicons
                  name="arrow-forward"
                  size={18}
                  color={
                    pin.length === 4
                      ? theme.colors.primaryForeground
                      : theme.colors.textSecondary
                  }
                />
              </TouchableOpacity>
            </View>
          </View>
        )}
      </>
    );
  }
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    justifyContent: "center",
    alignItems: "center",
    padding: isWeb ? (isSmallScreen ? 10 : 20) : 10, // Reduced padding on mobile
  },
  container: {
    borderRadius: isWeb ? 16 : 12,
    borderWidth: 1,
    elevation: 8,
    shadowColor: "rgba(0, 0, 0, 0.25)",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    // Ensure minimum height on mobile
    minHeight: isWeb ? undefined : 400,
    // Remove fixed padding to allow ScrollView to handle content spacing
  },
  scrollContainer: {
    flex: 1,
    // Ensure ScrollView takes available space
    minHeight: isWeb ? undefined : 300,
  },
  scrollContent: {
    padding: 20,
    paddingTop: isWeb ? 60 : 40, // Reduced padding on mobile
    paddingBottom: 40, // Ensure bottom content is visible
    minHeight: isWeb ? "auto" : undefined,
  },
  closeButton: {
    position: "absolute",
    top: 12,
    right: 12,
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: "center",
    justifyContent: "center",
    zIndex: 1,
  },
  title: {
    textAlign: "center",
    marginBottom: 6,
    marginTop: isWeb ? (isSmallScreen ? 20 : 40) : 40,
    fontSize: isWeb ? (isSmallScreen ? 16 : 18) : 18,
    fontWeight: "600",
  },
  description: {
    textAlign: "center",
    lineHeight: isWeb ? (isSmallScreen ? 16 : 18) : 18,
    marginBottom: isWeb ? (isSmallScreen ? 16 : 20) : 20,
    fontSize: isWeb ? (isSmallScreen ? 13 : 14) : 14,
  },
  userContainer: {
    borderRadius: 10,
    borderWidth: 1,
    padding: 12,
    marginBottom: 18,
  },
  userRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 10,
  },
  avatarText: {
    fontWeight: "600",
    fontSize: 16,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontWeight: "600",
    marginBottom: 2,
    fontSize: 16,
  },
  switchLabel: {
    fontSize: 12,
  },
  chevronContainer: {
    padding: 4,
  },
  pinSection: {
    alignItems: "center",
    marginBottom: isWeb ? (isSmallScreen ? 16 : 24) : 24,
  },
  pinBoxes: {
    flexDirection: "row",
    gap: isWeb ? (isSmallScreen ? 8 : 12) : 12,
    marginBottom: 8,
  },
  pinBox: {
    width: isWeb ? (isSmallScreen ? 40 : 48) : 48,
    height: isWeb ? (isSmallScreen ? 40 : 48) : 48,
    borderWidth: 2,
    borderRadius: isWeb ? (isSmallScreen ? 8 : 10) : 10,
    alignItems: "center",
    justifyContent: "center",
  },
  pinDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  errorText: {
    textAlign: "center",
    marginTop: 8,
    fontSize: 12,
  },
  keypad: {
    width: "100%",
    marginBottom: isWeb ? (isSmallScreen ? 8 : 12) : 12,
  },
  keypadRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: isWeb ? (isSmallScreen ? 8 : 12) : 12,
    paddingHorizontal: isWeb ? (isSmallScreen ? 8 : 16) : 16,
  },
  keypadButton: {
    width: isWeb ? (isSmallScreen ? 50 : isMediumScreen ? 60 : 70) : 70,
    height: isWeb ? (isSmallScreen ? 50 : isMediumScreen ? 60 : 70) : 70,
    borderRadius: isWeb ? (isSmallScreen ? 25 : isMediumScreen ? 30 : 35) : 35,
    borderWidth: 1,
    alignItems: "center",
    justifyContent: "center",
    // Ensure proper button appearance on mobile
    minWidth: 60,
    minHeight: 60,
  },
  keypadButtonShadow: {
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.12,
    shadowRadius: 3,
  },
  keypadText: {
    fontWeight: "600",
    fontSize: 20,
  },
  loading: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 12,
    gap: 6,
  },
  loadingText: {
    fontSize: 12,
  },
  progressContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 12,
    gap: 6,
  },
  progressText: {
    fontSize: 12,
  },
  // User selection styles
  searchSection: {
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderRadius: 8,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    paddingVertical: 4,
  },
  staffList: {
    maxHeight: isWeb ? (isSmallScreen ? 120 : 180) : 180,
    marginBottom: isWeb ? (isSmallScreen ? 12 : 16) : 16,
  },
  staffItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 6,
  },
  staffInfo: {
    flex: 1,
  },
  staffName: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 2,
  },
  staffRole: {
    fontSize: 12,
  },
  staffStatus: {
    alignItems: "flex-end",
  },
  pinBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    gap: 2,
  },
  pinBadgeText: {
    fontSize: 10,
    fontWeight: "600",
    color: "white",
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderRadius: 8,
    gap: 6,
  },
  backButtonText: {
    fontSize: 14,
    fontWeight: "500",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    position: "absolute",
    top: 16,
    left: 16,
    right: 16,
    zIndex: 1,
  },
  headerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "transparent",
  },
  hiddenInput: {
    position: "absolute",
    left: -9999,
    opacity: 0,
    height: 1,
    width: 1,
  },
  keyboardModeText: {
    textAlign: "center",
    marginTop: 8,
    fontSize: 12,
    fontStyle: "italic",
  },
});
